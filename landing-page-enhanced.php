<?php
require_once 'php/config.php';

// Get product ID from URL parameter
$productId = $_GET['id'] ?? null;

if (!$productId) {
    header('Location: /error.html');
    exit;
}

try {
    // Get product and landing page data
    $stmt = $conn->prepare("
        SELECT p.*, lp.titre as landing_title, lp.contenu_droit, lp.contenu_gauche, lp.lien_url
        FROM produits p
        LEFT JOIN landing_pages lp ON p.id = lp.produit_id
        WHERE p.id = ? AND p.actif = 1
    ");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        header('Location: /error.html');
        exit;
    }

    // Get landing page images if landing page exists
    $images = [];
    if ($product['landing_title']) {
        $stmt = $conn->prepare("
            SELECT lpi.image_url
            FROM landing_pages lp
            JOIN landing_page_images lpi ON lp.id = lpi.landing_page_id
            WHERE lp.produit_id = ?
            ORDER BY lpi.ordre
        ");
        $stmt->execute([$productId]);
        $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    // If no images, use product image
    if (empty($images) && $product['image_url']) {
        $images = [$product['image_url']];
    }

    // Set page title
    $pageTitle = $product['landing_title'] ?: $product['titre'];
} catch (PDOException $e) {
    header('Location: /error.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo htmlspecialchars(strip_tags($product['description'] ?: $product['titre'])); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($product['type'] . ', ' . $product['titre']); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(strip_tags($product['description'] ?: $product['titre'])); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($images[0] ?? '/images/default-product.jpg'); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>">
    <meta property="og:type" content="product">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars(strip_tags($product['description'] ?: $product['titre'])); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($images[0] ?? '/images/default-product.jpg'); ?>">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Swiper CSS for image slider -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">

    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e293b;
            --accent-color: #f59e0b;
            --text-color: #374151;
            --light-bg: #f8fafc;
            --border-color: #e5e7eb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--light-bg);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo h1 {
            color: var(--primary-color);
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-color);
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .price {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 2rem;
        }

        .cta-button {
            display: inline-block;
            background: var(--accent-color);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* Image Gallery */
        .gallery-section {
            padding: 4rem 0;
            background: white;
        }

        .swiper {
            width: 100%;
            height: 400px;
            margin-bottom: 2rem;
        }

        .swiper-slide {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .swiper-slide img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        .content-block {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        }

        .content-block h2 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .content-block h3 {
            color: var(--primary-color);
            margin: 1.5rem 0 1rem;
        }

        .content-block ul {
            margin: 1rem 0;
            padding-right: 1.5rem;
        }

        .content-block li {
            margin-bottom: 0.5rem;
        }

        .content-block blockquote {
            background: var(--light-bg);
            padding: 1rem;
            border-right: 4px solid var(--primary-color);
            margin: 1rem 0;
            font-style: italic;
        }

        .content-block cite {
            display: block;
            text-align: left;
            margin-top: 0.5rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Social Share */
        .social-share {
            background: var(--secondary-color);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }

        .share-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .share-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            color: white;
            border-radius: 6px;
            transition: transform 0.3s;
        }

        .share-btn:hover {
            transform: translateY(-2px);
        }

        .share-btn.facebook {
            background: #1877f2;
        }

        .share-btn.twitter {
            background: #1da1f2;
        }

        .share-btn.whatsapp {
            background: #25d366;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .nav-links {
                display: none;
            }

            .share-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">
                    <h1>متجر مصعب</h1>
                </div>
                <ul class="nav-links">
                    <li><a href="/">الرئيسية</a></li>
                    <li><a href="/cart.html">السلة</a></li>
                    <li><a href="#contact">اتصل بنا</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1><?php echo htmlspecialchars($pageTitle); ?></h1>
            <?php if ($product['description']): ?>
                <p><?php echo htmlspecialchars($product['description']); ?></p>
            <?php endif; ?>
            <div class="price"><?php echo number_format($product['prix'], 2); ?> دج</div>
            <a href="#order" class="cta-button">
                <i class="fas fa-shopping-cart"></i>
                اطلب الآن
            </a>
        </div>
    </section>

    <!-- Image Gallery -->
    <?php if (!empty($images)): ?>
        <section class="gallery-section">
            <div class="container">
                <div class="swiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($images as $image): ?>
                            <div class="swiper-slide">
                                <img src="<?php echo htmlspecialchars($image); ?>" alt="<?php echo htmlspecialchars($product['titre']); ?>">
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- Content Sections -->
    <?php if ($product['contenu_droit'] || $product['contenu_gauche']): ?>
        <section class="content-section">
            <div class="container">
                <div class="content-grid">
                    <?php if ($product['contenu_droit']): ?>
                        <div class="content-block">
                            <?php echo $product['contenu_droit']; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($product['contenu_gauche']): ?>
                        <div class="content-block">
                            <?php echo $product['contenu_gauche']; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- Product Details -->
    <section class="content-section" style="background: white;">
        <div class="container">
            <div class="content-grid">
                <div class="content-block">
                    <h2>تفاصيل المنتج</h2>
                    <ul>
                        <li><strong>النوع:</strong> <?php
                                                    $types = ['book' => 'كتاب', 'laptop' => 'حاسوب محمول', 'bag' => 'حقيبة'];
                                                    echo $types[$product['type']] ?? $product['type'];
                                                    ?></li>
                        <li><strong>السعر:</strong> <?php echo number_format($product['prix'], 2); ?> دج</li>
                        <li><strong>المخزون:</strong> <?php echo $product['stock']; ?> قطعة</li>

                        <?php if ($product['type'] === 'book' && $product['auteur']): ?>
                            <li><strong>المؤلف:</strong> <?php echo htmlspecialchars($product['auteur']); ?></li>
                        <?php endif; ?>

                        <?php if ($product['type'] === 'laptop'): ?>
                            <?php if ($product['processeur']): ?>
                                <li><strong>المعالج:</strong> <?php echo htmlspecialchars($product['processeur']); ?></li>
                            <?php endif; ?>
                            <?php if ($product['ram']): ?>
                                <li><strong>الذاكرة:</strong> <?php echo htmlspecialchars($product['ram']); ?></li>
                            <?php endif; ?>
                            <?php if ($product['stockage']): ?>
                                <li><strong>التخزين:</strong> <?php echo htmlspecialchars($product['stockage']); ?></li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if ($product['type'] === 'bag'): ?>
                            <?php if ($product['materiel']): ?>
                                <li><strong>المواد:</strong> <?php echo htmlspecialchars($product['materiel']); ?></li>
                            <?php endif; ?>
                            <?php if ($product['capacite']): ?>
                                <li><strong>السعة:</strong> <?php echo htmlspecialchars($product['capacite']); ?></li>
                            <?php endif; ?>
                        <?php endif; ?>
                    </ul>
                </div>

                <div class="content-block" id="order">
                    <h2>اطلب الآن</h2>
                    <form id="orderForm" style="display: flex; flex-direction: column; gap: 1rem;">
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                        <input type="text" name="customer_name" placeholder="الاسم الكامل" required style="padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 6px;">
                        <input type="tel" name="customer_phone" placeholder="رقم الهاتف" required style="padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 6px;">
                        <textarea name="customer_address" placeholder="العنوان" required style="padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 6px; min-height: 80px;"></textarea>
                        <input type="number" name="quantity" value="1" min="1" max="<?php echo $product['stock']; ?>" style="padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 6px;">
                        <button type="submit" class="cta-button" style="border: none; cursor: pointer;">
                            <i class="fas fa-paper-plane"></i>
                            إرسال الطلب
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Share -->
    <section class="social-share">
        <div class="container">
            <h2>شارك هذا المنتج</h2>
            <div class="share-buttons">
                <a href="#" class="share-btn facebook" onclick="shareOnFacebook()">
                    <i class="fab fa-facebook-f"></i>
                    فيسبوك
                </a>
                <a href="#" class="share-btn twitter" onclick="shareOnTwitter()">
                    <i class="fab fa-twitter"></i>
                    تويتر
                </a>
                <a href="#" class="share-btn whatsapp" onclick="shareOnWhatsApp()">
                    <i class="fab fa-whatsapp"></i>
                    واتساب
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--secondary-color); color: white; padding: 2rem 0; text-align: center;">
        <div class="container">
            <p>&copy; 2024 متجر مصعب. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script>
        // Initialize Swiper
        const swiper = new Swiper('.swiper', {
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            }
        });

        // Social sharing functions
        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent('<?php echo addslashes($pageTitle); ?>');
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&t=${title}`, '_blank', 'width=600,height=400');
        }

        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('<?php echo addslashes($pageTitle); ?>');
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank', 'width=600,height=400');
        }

        function shareOnWhatsApp() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('<?php echo addslashes($pageTitle); ?> - ${url}');
            window.open(`https://wa.me/?text=${text}`, '_blank');
        }

        // Order form handling
        document.getElementById('orderForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            submitButton.disabled = true;

            try {
                const response = await fetch('php/api/orders.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('تم إرسال طلبك بنجاح! سنتواصل معك قريباً.');
                    this.reset();
                } else {
                    alert('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            } finally {
                // Reset button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>

</html>
