<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الدفع - لوحة التحكم</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .payment-container {
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .payment-method {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .payment-method.active {
            border-color: #28a745;
            background: #f8fff9;
        }
        .payment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .payment-info {
            display: flex;
            align-items: center;
        }
        .payment-icon {
            font-size: 2.5em;
            margin-left: 20px;
            color: #007bff;
        }
        .payment-details h3 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .payment-details p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .toggle-switch.active {
            background: #28a745;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        .toggle-switch.active::after {
            transform: translateX(30px);
        }
        .payment-config {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .payment-config.show {
            display: block;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-test {
            background: #ffc107;
            color: #333;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .security-notice {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .security-notice h4 {
            color: #0c5460;
            margin: 0 0 10px 0;
        }
        .security-notice p {
            color: #0c5460;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <header class="admin-header">
            <h1>💳 إعدادات الدفع</h1>
            <div class="header-actions">
                <button onclick="window.history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة
                </button>
                <button onclick="saveAllSettings()" class="btn btn-success">
                    <i class="fas fa-save"></i> حفظ جميع الإعدادات
                </button>
            </div>
        </header>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="activePaymentMethods">0</div>
                <div class="stat-label">طرق الدفع النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTransactions">0</div>
                <div class="stat-label">إجمالي المعاملات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="security-notice">
            <h4><i class="fas fa-shield-alt"></i> تنبيه أمني مهم</h4>
            <p>• لا تشارك بيانات الدفع الحساسة مع أي شخص</p>
            <p>• تأكد من استخدام اتصال آمن (HTTPS) في الإنتاج</p>
            <p>• قم بتشفير جميع البيانات الحساسة في قاعدة البيانات</p>
            <p>• راجع إعدادات الأمان بانتظام</p>
        </div>

        <!-- Cash on Delivery -->
        <div class="payment-container">
            <div class="payment-method" id="cod-method">
                <div class="payment-header">
                    <div class="payment-info">
                        <i class="fas fa-hand-holding-usd payment-icon"></i>
                        <div class="payment-details">
                            <h3>الدفع عند الاستلام</h3>
                            <p>يدفع العميل عند استلام المنتج</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="togglePaymentMethod('cod')"></div>
                </div>
                
                <div class="payment-config show" id="cod-config">
                    <div class="form-row">
                        <div class="form-group">
                            <label>رسوم إضافية (دج)</label>
                            <input type="number" id="cod-fee" value="0" min="0" step="100">
                        </div>
                        <div class="form-group">
                            <label>الحد الأدنى للطلب (دج)</label>
                            <input type="number" id="cod-min-order" value="1000" min="0" step="100">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>المناطق المتاحة</label>
                        <textarea id="cod-areas" rows="3" placeholder="الجزائر العاصمة، وهران، قسنطينة...">جميع ولايات الجزائر</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>ملاحظات للعميل</label>
                        <textarea id="cod-notes" rows="2" placeholder="ملاحظات إضافية">يرجى تحضير المبلغ المطلوب عند الاستلام</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Transfer -->
        <div class="payment-container">
            <div class="payment-method" id="bank-method">
                <div class="payment-header">
                    <div class="payment-info">
                        <i class="fas fa-university payment-icon"></i>
                        <div class="payment-details">
                            <h3>التحويل البنكي</h3>
                            <p>تحويل مباشر إلى الحساب البنكي</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="togglePaymentMethod('bank')"></div>
                </div>
                
                <div class="payment-config" id="bank-config">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم البنك</label>
                            <input type="text" id="bank-name" placeholder="مثال: بنك الجزائر الخارجي">
                        </div>
                        <div class="form-group">
                            <label>رقم الحساب</label>
                            <input type="text" id="bank-account" placeholder="**********">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم صاحب الحساب</label>
                            <input type="text" id="bank-holder" placeholder="الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label>رمز البنك (SWIFT/BIC)</label>
                            <input type="text" id="bank-swift" placeholder="BADRDZAL">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>تعليمات التحويل</label>
                        <textarea id="bank-instructions" rows="3" placeholder="تعليمات للعميل حول كيفية إجراء التحويل">يرجى إرسال إيصال التحويل عبر الواتساب أو البريد الإلكتروني</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- CCP (Postal Account) -->
        <div class="payment-container">
            <div class="payment-method" id="ccp-method">
                <div class="payment-header">
                    <div class="payment-info">
                        <i class="fas fa-mail-bulk payment-icon"></i>
                        <div class="payment-details">
                            <h3>الحساب الجاري البريدي (CCP)</h3>
                            <p>تحويل عبر بريد الجزائر</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="togglePaymentMethod('ccp')"></div>
                </div>
                
                <div class="payment-config" id="ccp-config">
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم الحساب الجاري البريدي</label>
                            <input type="text" id="ccp-number" placeholder="مثال: 1234567890">
                        </div>
                        <div class="form-group">
                            <label>مفتاح الحساب</label>
                            <input type="text" id="ccp-key" placeholder="مثال: 12">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>اسم صاحب الحساب</label>
                        <input type="text" id="ccp-holder" placeholder="الاسم الكامل كما يظهر في الحساب">
                    </div>
                    
                    <div class="form-group">
                        <label>تعليمات الدفع</label>
                        <textarea id="ccp-instructions" rows="3" placeholder="تعليمات للعميل">يرجى الاحتفاظ بإيصال التحويل وإرساله لنا للتأكيد</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Payment -->
        <div class="payment-container">
            <div class="payment-method" id="mobile-method">
                <div class="payment-header">
                    <div class="payment-info">
                        <i class="fas fa-mobile-alt payment-icon"></i>
                        <div class="payment-details">
                            <h3>الدفع عبر الهاتف</h3>
                            <p>تحويل عبر تطبيقات الهاتف المحمول</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="togglePaymentMethod('mobile')"></div>
                </div>
                
                <div class="payment-config" id="mobile-config">
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم الهاتف للتحويل</label>
                            <input type="tel" id="mobile-number" placeholder="**********">
                        </div>
                        <div class="form-group">
                            <label>اسم التطبيق</label>
                            <select id="mobile-app">
                                <option value="mobilis-money">Mobilis Money</option>
                                <option value="djezzy-pay">Djezzy Pay</option>
                                <option value="ooredoo-money">Ooredoo Money</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>اسم صاحب الحساب</label>
                        <input type="text" id="mobile-holder" placeholder="الاسم المسجل في التطبيق">
                    </div>
                    
                    <div class="form-group">
                        <label>تعليمات الدفع</label>
                        <textarea id="mobile-instructions" rows="3" placeholder="تعليمات للعميل">يرجى إرسال لقطة شاشة لإيصال التحويل</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- General Settings -->
        <div class="payment-container">
            <h3>⚙️ الإعدادات العامة</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label>العملة الافتراضية</label>
                    <select id="default-currency">
                        <option value="DZD">دينار جزائري (DZD)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                        <option value="EUR">يورو (EUR)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>مهلة انتظار الدفع (أيام)</label>
                    <input type="number" id="payment-timeout" value="3" min="1" max="30">
                </div>
            </div>
            
            <div class="form-group">
                <label>رسالة شكر بعد الدفع</label>
                <textarea id="thank-you-message" rows="3">شكراً لك على طلبك! سنتواصل معك قريباً لتأكيد الطلب وترتيب التوصيل.</textarea>
            </div>
            
            <div class="form-group">
                <button type="button" class="btn btn-test" onclick="testPaymentSettings()">
                    <i class="fas fa-vial"></i> اختبار الإعدادات
                </button>
                <button type="button" class="btn btn-primary" onclick="exportSettings()">
                    <i class="fas fa-download"></i> تصدير الإعدادات
                </button>
                <button type="button" class="btn btn-secondary" onclick="importSettings()">
                    <i class="fas fa-upload"></i> استيراد الإعدادات
                </button>
            </div>
        </div>
    </div>

    <script src="js/payment-settings.js"></script>
</body>
</html>
