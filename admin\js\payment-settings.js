/**
 * Payment Settings Management JavaScript
 * Handles payment method configuration and settings
 */

let paymentSettings = {
    cod: { enabled: true, fee: 0, minOrder: 1000, areas: 'جميع ولايات الجزائر', notes: 'يرجى تحضير المبلغ المطلوب عند الاستلام' },
    bank: { enabled: false, name: '', account: '', holder: '', swift: '', instructions: '' },
    ccp: { enabled: false, number: '', key: '', holder: '', instructions: '' },
    mobile: { enabled: false, number: '', app: 'mobilis-money', holder: '', instructions: '' },
    general: { currency: 'DZD', timeout: 3, thankYouMessage: 'شكراً لك على طلبك! سنتواصل معك قريباً لتأكيد الطلب وترتيب التوصيل.' }
};

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('💳 Payment settings initialized');
    loadPaymentSettings();
    updateStatistics();
    setupEventListeners();

    // Initialize shipping functionality
    setTimeout(initializeShipping, 500); // Delay to ensure DOM is ready
});

// Setup event listeners
function setupEventListeners() {
    // Auto-save on input changes
    document.addEventListener('input', function(event) {
        if (event.target.matches('input, textarea, select')) {
            autoSaveSettings();
        }
    });

    // Prevent form submission
    document.addEventListener('submit', function(event) {
        event.preventDefault();
    });
}

// Load payment settings from localStorage or API
function loadPaymentSettings() {
    try {
        // Try to load from localStorage first
        const savedSettings = localStorage.getItem('paymentSettings');
        if (savedSettings) {
            paymentSettings = { ...paymentSettings, ...JSON.parse(savedSettings) };
        }

        // Apply settings to UI
        applySettingsToUI();

        // Load from API if available
        loadFromAPI();
    } catch (error) {
        console.error('Error loading payment settings:', error);
        showError('خطأ في تحميل إعدادات الدفع');
    }
}

// Apply settings to UI elements
function applySettingsToUI() {
    // COD settings
    document.getElementById('cod-fee').value = paymentSettings.cod.fee || 0;
    document.getElementById('cod-min-order').value = paymentSettings.cod.minOrder || 1000;
    document.getElementById('cod-areas').value = paymentSettings.cod.areas || '';
    document.getElementById('cod-notes').value = paymentSettings.cod.notes || '';

    // Bank settings
    document.getElementById('bank-name').value = paymentSettings.bank.name || '';
    document.getElementById('bank-account').value = paymentSettings.bank.account || '';
    document.getElementById('bank-holder').value = paymentSettings.bank.holder || '';
    document.getElementById('bank-swift').value = paymentSettings.bank.swift || '';
    document.getElementById('bank-instructions').value = paymentSettings.bank.instructions || '';

    // CCP settings
    document.getElementById('ccp-number').value = paymentSettings.ccp.number || '';
    document.getElementById('ccp-key').value = paymentSettings.ccp.key || '';
    document.getElementById('ccp-holder').value = paymentSettings.ccp.holder || '';
    document.getElementById('ccp-instructions').value = paymentSettings.ccp.instructions || '';

    // Mobile settings
    document.getElementById('mobile-number').value = paymentSettings.mobile.number || '';
    document.getElementById('mobile-app').value = paymentSettings.mobile.app || 'mobilis-money';
    document.getElementById('mobile-holder').value = paymentSettings.mobile.holder || '';
    document.getElementById('mobile-instructions').value = paymentSettings.mobile.instructions || '';

    // General settings
    document.getElementById('default-currency').value = paymentSettings.general.currency || 'DZD';
    document.getElementById('payment-timeout').value = paymentSettings.general.timeout || 3;
    document.getElementById('thank-you-message').value = paymentSettings.general.thankYouMessage || '';

    // Update toggle switches
    updateToggleSwitch('cod', paymentSettings.cod.enabled);
    updateToggleSwitch('bank', paymentSettings.bank.enabled);
    updateToggleSwitch('ccp', paymentSettings.ccp.enabled);
    updateToggleSwitch('mobile', paymentSettings.mobile.enabled);
}

// Load settings from API
async function loadFromAPI() {
    try {
        console.log('Loading payment settings from API...');
        const response = await fetch('../php/api/payment-settings.php');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        if (data.success && data.settings) {
            paymentSettings = { ...paymentSettings, ...data.settings };
            applySettingsToUI();

            // Update statistics from API
            if (data.statistics) {
                document.getElementById('activePaymentMethods').textContent = data.statistics.active_methods || 0;
                document.getElementById('totalTransactions').textContent = data.statistics.total_transactions || 0;
                document.getElementById('successRate').textContent = (data.statistics.success_rate || 0) + '%';
            }

            console.log('Payment settings loaded from API successfully');
        }
    } catch (error) {
        console.error('Error loading from API:', error);
        console.log('Falling back to localStorage settings');
    }
}

// Toggle payment method
function togglePaymentMethod(method) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);

    const isCurrentlyActive = toggleSwitch.classList.contains('active');
    const newState = !isCurrentlyActive;

    // Update toggle switch
    updateToggleSwitch(method, newState);

    // Update settings
    paymentSettings[method].enabled = newState;

    // Save settings
    saveSettings();

    // Update statistics
    updateStatistics();

    showSuccess(`تم ${newState ? 'تفعيل' : 'إلغاء'} طريقة الدفع بنجاح`);
}

// Update toggle switch appearance
function updateToggleSwitch(method, enabled) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);

    if (enabled) {
        toggleSwitch.classList.add('active');
        config.classList.add('show');
        paymentMethod.classList.add('active');
    } else {
        toggleSwitch.classList.remove('active');
        config.classList.remove('show');
        paymentMethod.classList.remove('active');
    }
}

// Auto-save settings on input change
function autoSaveSettings() {
    // Collect all form data
    collectFormData();

    // Save to localStorage
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));

    console.log('Settings auto-saved');
}

// Collect form data into settings object
function collectFormData() {
    // COD settings
    paymentSettings.cod.fee = parseFloat(document.getElementById('cod-fee').value) || 0;
    paymentSettings.cod.minOrder = parseFloat(document.getElementById('cod-min-order').value) || 0;
    paymentSettings.cod.areas = document.getElementById('cod-areas').value;
    paymentSettings.cod.notes = document.getElementById('cod-notes').value;

    // Bank settings
    paymentSettings.bank.name = document.getElementById('bank-name').value;
    paymentSettings.bank.account = document.getElementById('bank-account').value;
    paymentSettings.bank.holder = document.getElementById('bank-holder').value;
    paymentSettings.bank.swift = document.getElementById('bank-swift').value;
    paymentSettings.bank.instructions = document.getElementById('bank-instructions').value;

    // CCP settings
    paymentSettings.ccp.number = document.getElementById('ccp-number').value;
    paymentSettings.ccp.key = document.getElementById('ccp-key').value;
    paymentSettings.ccp.holder = document.getElementById('ccp-holder').value;
    paymentSettings.ccp.instructions = document.getElementById('ccp-instructions').value;

    // Mobile settings
    paymentSettings.mobile.number = document.getElementById('mobile-number').value;
    paymentSettings.mobile.app = document.getElementById('mobile-app').value;
    paymentSettings.mobile.holder = document.getElementById('mobile-holder').value;
    paymentSettings.mobile.instructions = document.getElementById('mobile-instructions').value;

    // General settings
    paymentSettings.general.currency = document.getElementById('default-currency').value;
    paymentSettings.general.timeout = parseInt(document.getElementById('payment-timeout').value) || 3;
    paymentSettings.general.thankYouMessage = document.getElementById('thank-you-message').value;
}

// Save all settings
async function saveAllSettings() {
    try {
        showLoading('جاري حفظ الإعدادات...');

        // Collect form data
        collectFormData();

        // Save to localStorage
        localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));

        // Save to API
        try {
            const response = await fetch('../php/api/payment-settings.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(paymentSettings)
            });

            const result = await response.json();

            if (result.success) {
                showSuccess('تم حفظ جميع إعدادات الدفع بنجاح في قاعدة البيانات');
            } else {
                throw new Error(result.message || 'فشل في حفظ الإعدادات');
            }
        } catch (apiError) {
            console.error('API save error:', apiError);
            showSuccess('تم حفظ الإعدادات محلياً (قاعدة البيانات غير متاحة)');
        updateStatistics();

    } catch (error) {
        console.error('Error saving settings:', error);
        showError('خطأ في حفظ الإعدادات');
    } finally {
        hideLoading();
    }
}

// Save settings (internal function)
function saveSettings() {
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
}

// Update statistics
function updateStatistics() {
    const activeMethods = Object.values(paymentSettings).filter(method => method.enabled).length - 1; // -1 for general settings
    const totalTransactions = Math.floor(Math.random() * 1000) + 100; // Mock data
    const successRate = Math.floor(Math.random() * 20) + 80; // Mock data

    document.getElementById('activePaymentMethods').textContent = activeMethods;
    document.getElementById('totalTransactions').textContent = totalTransactions;
    document.getElementById('successRate').textContent = successRate + '%';
}

// Test payment settings
function testPaymentSettings() {
    showLoading('جاري اختبار الإعدادات...');

    setTimeout(() => {
        const activeMethods = Object.keys(paymentSettings).filter(key =>
            key !== 'general' && paymentSettings[key].enabled
        );

        if (activeMethods.length === 0) {
            showError('لا توجد طرق دفع مفعلة للاختبار');
            return;
        }

        let testResults = '🧪 نتائج الاختبار:\n\n';

        activeMethods.forEach(method => {
            const methodName = getMethodName(method);
            const isValid = validateMethodSettings(method);
            testResults += `${isValid ? '✅' : '❌'} ${methodName}: ${isValid ? 'جاهز' : 'يحتاج إعداد'}\n`;
        });

        alert(testResults);
        hideLoading();
    }, 2000);
}

// Get method display name
function getMethodName(method) {
    const names = {
        cod: 'الدفع عند الاستلام',
        bank: 'التحويل البنكي',
        ccp: 'الحساب الجاري البريدي',
        mobile: 'الدفع عبر الهاتف'
    };
    return names[method] || method;
}

// Validate method settings
function validateMethodSettings(method) {
    switch (method) {
        case 'cod':
            return paymentSettings.cod.areas && paymentSettings.cod.notes;
        case 'bank':
            return paymentSettings.bank.name && paymentSettings.bank.account && paymentSettings.bank.holder;
        case 'ccp':
            return paymentSettings.ccp.number && paymentSettings.ccp.key && paymentSettings.ccp.holder;
        case 'mobile':
            return paymentSettings.mobile.number && paymentSettings.mobile.holder;
        default:
            return false;
    }
}

// Export settings
function exportSettings() {
    const dataStr = JSON.stringify(paymentSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'payment-settings-' + new Date().toISOString().split('T')[0] + '.json';
    link.click();

    URL.revokeObjectURL(url);
    showSuccess('تم تصدير الإعدادات بنجاح');
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedSettings = JSON.parse(e.target.result);
                paymentSettings = { ...paymentSettings, ...importedSettings };
                applySettingsToUI();
                saveSettings();
                updateStatistics();
                showSuccess('تم استيراد الإعدادات بنجاح');
            } catch (error) {
                showError('خطأ في قراءة ملف الإعدادات');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

// Utility functions
function showLoading(message) {
    console.log('Loading:', message);
    // You can implement a loading spinner here
}

function hideLoading() {
    console.log('Loading finished');
}

function showSuccess(message) {
    // Create a temporary success alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 3000);
}

function showError(message) {
    // Create a temporary error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger';
    alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Shipping functionality
async function loadWilayas() {
    try {
        console.log('Loading wilayas from API...');
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=wilayas');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Wilayas API Response:', data);

        if (data.success && data.wilayas) {
            const wilayaSelect = document.getElementById('wilayaSelect');
            if (wilayaSelect) {
                wilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';

                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name} (${wilaya.zone_name} - ${wilaya.shipping_cost} دج)`;
                    wilayaSelect.appendChild(option);
                });

                console.log(`Loaded ${data.wilayas.length} wilayas`);
            }
        }
    } catch (error) {
        console.error('Error loading wilayas:', error);
        showError('فشل في تحميل قائمة الولايات');
    }
}

async function loadShippingZones() {
    try {
        console.log('Loading shipping zones...');
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=zones');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Zones API Response:', data);

        if (data.success && data.zones) {
            const zonesContainer = document.getElementById('shippingZones');
            if (zonesContainer) {
                zonesContainer.innerHTML = '';

                data.zones.forEach(zone => {
                    const zoneCard = document.createElement('div');
                    zoneCard.className = 'zone-card';
                    zoneCard.innerHTML = `
                        <div class="zone-title">${zone.zone_name}</div>
                        <div class="zone-details">${zone.wilaya_count} ولاية</div>
                        <div class="zone-cost">${zone.min_cost} - ${zone.max_cost} دج</div>
                        <div class="zone-details">حسب المسافة والوزن</div>
                    `;
                    zonesContainer.appendChild(zoneCard);
                });

                console.log(`Loaded ${data.zones.length} shipping zones`);
            }
        }
    } catch (error) {
        console.error('Error loading shipping zones:', error);
        showError('فشل في تحميل مناطق الشحن');
    }
}

async function calculateShippingCost() {
    const wilayaCode = document.getElementById('wilayaSelect').value;
    const weight = parseFloat(document.getElementById('packageWeight').value) || 1.0;

    if (!wilayaCode) {
        showError('يرجى اختيار الولاية أولاً');
        return;
    }

    if (weight < 0.1 || weight > 30) {
        showError('الوزن يجب أن يكون بين 0.1 و 30 كيلوغرام');
        return;
    }

    try {
        console.log(`Calculating shipping for wilaya: ${wilayaCode}, weight: ${weight}kg`);

        const response = await fetch(`../php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=${weight}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Shipping calculation result:', data);

        if (data.success) {
            displayShippingResult(data);
        } else {
            showError(data.message || 'فشل في حساب تكلفة الشحن');
        }
    } catch (error) {
        console.error('Error calculating shipping:', error);
        showError('حدث خطأ أثناء حساب تكلفة الشحن');
    }
}

function displayShippingResult(data) {
    const resultDiv = document.getElementById('shippingResult');
    const zoneInfo = data.zone_info;
    const costBreakdown = data.cost_breakdown;

    // Update zone information
    document.getElementById('zoneName').textContent = zoneInfo.zone_name;
    document.getElementById('wilayaName').textContent = zoneInfo.wilaya_name;
    document.getElementById('deliveryTime').textContent = zoneInfo.delivery_time;

    // Update cost breakdown
    document.getElementById('baseCost').textContent = costBreakdown.base_cost.toFixed(2);
    document.getElementById('weightDisplay').textContent = costBreakdown.weight.toFixed(1);
    document.getElementById('totalCost').textContent = costBreakdown.total_cost.toFixed(2);

    // Show/hide surcharge information
    const surchargeItem = document.getElementById('surchargeItem');
    if (costBreakdown.extra_weight > 0) {
        document.getElementById('surchargeAmount').textContent = costBreakdown.surcharge_total.toFixed(2);
        surchargeItem.style.display = 'block';
    } else {
        surchargeItem.style.display = 'none';
    }

    // Show the result
    resultDiv.style.display = 'block';
    resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Initialize shipping functionality
function initializeShipping() {
    // Load shipping data
    loadWilayas();
    loadShippingZones();

    // Add shipping calculator event listener
    const calculateBtn = document.getElementById('calculateShipping');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateShippingCost);
    }

    // Add real-time calculation on weight change
    const weightInput = document.getElementById('packageWeight');
    if (weightInput) {
        weightInput.addEventListener('input', function() {
            const wilayaCode = document.getElementById('wilayaSelect').value;
            if (wilayaCode && this.value) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateShippingCost, 500);
            }
        });
    }

    // Add real-time calculation on wilaya change
    const wilayaSelect = document.getElementById('wilayaSelect');
    if (wilayaSelect) {
        wilayaSelect.addEventListener('change', function() {
            if (this.value) {
                calculateShippingCost();
            } else {
                document.getElementById('shippingResult').style.display = 'none';
            }
        });
    }
}

console.log('💳 Payment settings script loaded');
