/**
 * Payment Settings Management JavaScript
 * Handles payment method configuration and settings
 */

let paymentSettings = {
    cod: { enabled: true, fee: 0, minOrder: 1000, areas: 'جميع ولايات الجزائر', notes: 'يرجى تحضير المبلغ المطلوب عند الاستلام' },
    bank: { enabled: false, name: '', account: '', holder: '', swift: '', instructions: '' },
    ccp: { enabled: false, number: '', key: '', holder: '', instructions: '' },
    mobile: { enabled: false, number: '', app: 'mobilis-money', holder: '', instructions: '' },
    general: { currency: 'DZD', timeout: 3, thankYouMessage: 'شكراً لك على طلبك! سنتواصل معك قريباً لتأكيد الطلب وترتيب التوصيل.' }
};

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('💳 Payment settings initialized');
    loadPaymentSettings();
    updateStatistics();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Auto-save on input changes
    document.addEventListener('input', function(event) {
        if (event.target.matches('input, textarea, select')) {
            autoSaveSettings();
        }
    });
    
    // Prevent form submission
    document.addEventListener('submit', function(event) {
        event.preventDefault();
    });
}

// Load payment settings from localStorage or API
function loadPaymentSettings() {
    try {
        // Try to load from localStorage first
        const savedSettings = localStorage.getItem('paymentSettings');
        if (savedSettings) {
            paymentSettings = { ...paymentSettings, ...JSON.parse(savedSettings) };
        }
        
        // Apply settings to UI
        applySettingsToUI();
        
        // Load from API if available
        loadFromAPI();
    } catch (error) {
        console.error('Error loading payment settings:', error);
        showError('خطأ في تحميل إعدادات الدفع');
    }
}

// Apply settings to UI elements
function applySettingsToUI() {
    // COD settings
    document.getElementById('cod-fee').value = paymentSettings.cod.fee || 0;
    document.getElementById('cod-min-order').value = paymentSettings.cod.minOrder || 1000;
    document.getElementById('cod-areas').value = paymentSettings.cod.areas || '';
    document.getElementById('cod-notes').value = paymentSettings.cod.notes || '';
    
    // Bank settings
    document.getElementById('bank-name').value = paymentSettings.bank.name || '';
    document.getElementById('bank-account').value = paymentSettings.bank.account || '';
    document.getElementById('bank-holder').value = paymentSettings.bank.holder || '';
    document.getElementById('bank-swift').value = paymentSettings.bank.swift || '';
    document.getElementById('bank-instructions').value = paymentSettings.bank.instructions || '';
    
    // CCP settings
    document.getElementById('ccp-number').value = paymentSettings.ccp.number || '';
    document.getElementById('ccp-key').value = paymentSettings.ccp.key || '';
    document.getElementById('ccp-holder').value = paymentSettings.ccp.holder || '';
    document.getElementById('ccp-instructions').value = paymentSettings.ccp.instructions || '';
    
    // Mobile settings
    document.getElementById('mobile-number').value = paymentSettings.mobile.number || '';
    document.getElementById('mobile-app').value = paymentSettings.mobile.app || 'mobilis-money';
    document.getElementById('mobile-holder').value = paymentSettings.mobile.holder || '';
    document.getElementById('mobile-instructions').value = paymentSettings.mobile.instructions || '';
    
    // General settings
    document.getElementById('default-currency').value = paymentSettings.general.currency || 'DZD';
    document.getElementById('payment-timeout').value = paymentSettings.general.timeout || 3;
    document.getElementById('thank-you-message').value = paymentSettings.general.thankYouMessage || '';
    
    // Update toggle switches
    updateToggleSwitch('cod', paymentSettings.cod.enabled);
    updateToggleSwitch('bank', paymentSettings.bank.enabled);
    updateToggleSwitch('ccp', paymentSettings.ccp.enabled);
    updateToggleSwitch('mobile', paymentSettings.mobile.enabled);
}

// Load settings from API
async function loadFromAPI() {
    try {
        // This would connect to your payment settings API
        // const response = await fetch('../php/api/payment-settings.php');
        // const data = await response.json();
        // if (data.success) {
        //     paymentSettings = { ...paymentSettings, ...data.settings };
        //     applySettingsToUI();
        // }
        console.log('API loading not implemented yet');
    } catch (error) {
        console.error('Error loading from API:', error);
    }
}

// Toggle payment method
function togglePaymentMethod(method) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);
    
    const isCurrentlyActive = toggleSwitch.classList.contains('active');
    const newState = !isCurrentlyActive;
    
    // Update toggle switch
    updateToggleSwitch(method, newState);
    
    // Update settings
    paymentSettings[method].enabled = newState;
    
    // Save settings
    saveSettings();
    
    // Update statistics
    updateStatistics();
    
    showSuccess(`تم ${newState ? 'تفعيل' : 'إلغاء'} طريقة الدفع بنجاح`);
}

// Update toggle switch appearance
function updateToggleSwitch(method, enabled) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);
    
    if (enabled) {
        toggleSwitch.classList.add('active');
        config.classList.add('show');
        paymentMethod.classList.add('active');
    } else {
        toggleSwitch.classList.remove('active');
        config.classList.remove('show');
        paymentMethod.classList.remove('active');
    }
}

// Auto-save settings on input change
function autoSaveSettings() {
    // Collect all form data
    collectFormData();
    
    // Save to localStorage
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
    
    console.log('Settings auto-saved');
}

// Collect form data into settings object
function collectFormData() {
    // COD settings
    paymentSettings.cod.fee = parseFloat(document.getElementById('cod-fee').value) || 0;
    paymentSettings.cod.minOrder = parseFloat(document.getElementById('cod-min-order').value) || 0;
    paymentSettings.cod.areas = document.getElementById('cod-areas').value;
    paymentSettings.cod.notes = document.getElementById('cod-notes').value;
    
    // Bank settings
    paymentSettings.bank.name = document.getElementById('bank-name').value;
    paymentSettings.bank.account = document.getElementById('bank-account').value;
    paymentSettings.bank.holder = document.getElementById('bank-holder').value;
    paymentSettings.bank.swift = document.getElementById('bank-swift').value;
    paymentSettings.bank.instructions = document.getElementById('bank-instructions').value;
    
    // CCP settings
    paymentSettings.ccp.number = document.getElementById('ccp-number').value;
    paymentSettings.ccp.key = document.getElementById('ccp-key').value;
    paymentSettings.ccp.holder = document.getElementById('ccp-holder').value;
    paymentSettings.ccp.instructions = document.getElementById('ccp-instructions').value;
    
    // Mobile settings
    paymentSettings.mobile.number = document.getElementById('mobile-number').value;
    paymentSettings.mobile.app = document.getElementById('mobile-app').value;
    paymentSettings.mobile.holder = document.getElementById('mobile-holder').value;
    paymentSettings.mobile.instructions = document.getElementById('mobile-instructions').value;
    
    // General settings
    paymentSettings.general.currency = document.getElementById('default-currency').value;
    paymentSettings.general.timeout = parseInt(document.getElementById('payment-timeout').value) || 3;
    paymentSettings.general.thankYouMessage = document.getElementById('thank-you-message').value;
}

// Save all settings
async function saveAllSettings() {
    try {
        showLoading('جاري حفظ الإعدادات...');
        
        // Collect form data
        collectFormData();
        
        // Save to localStorage
        localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
        
        // Save to API (if available)
        // const response = await fetch('../php/api/payment-settings.php', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(paymentSettings)
        // });
        
        showSuccess('تم حفظ جميع إعدادات الدفع بنجاح');
        updateStatistics();
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showError('خطأ في حفظ الإعدادات');
    } finally {
        hideLoading();
    }
}

// Save settings (internal function)
function saveSettings() {
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
}

// Update statistics
function updateStatistics() {
    const activeMethods = Object.values(paymentSettings).filter(method => method.enabled).length - 1; // -1 for general settings
    const totalTransactions = Math.floor(Math.random() * 1000) + 100; // Mock data
    const successRate = Math.floor(Math.random() * 20) + 80; // Mock data
    
    document.getElementById('activePaymentMethods').textContent = activeMethods;
    document.getElementById('totalTransactions').textContent = totalTransactions;
    document.getElementById('successRate').textContent = successRate + '%';
}

// Test payment settings
function testPaymentSettings() {
    showLoading('جاري اختبار الإعدادات...');
    
    setTimeout(() => {
        const activeMethods = Object.keys(paymentSettings).filter(key => 
            key !== 'general' && paymentSettings[key].enabled
        );
        
        if (activeMethods.length === 0) {
            showError('لا توجد طرق دفع مفعلة للاختبار');
            return;
        }
        
        let testResults = '🧪 نتائج الاختبار:\n\n';
        
        activeMethods.forEach(method => {
            const methodName = getMethodName(method);
            const isValid = validateMethodSettings(method);
            testResults += `${isValid ? '✅' : '❌'} ${methodName}: ${isValid ? 'جاهز' : 'يحتاج إعداد'}\n`;
        });
        
        alert(testResults);
        hideLoading();
    }, 2000);
}

// Get method display name
function getMethodName(method) {
    const names = {
        cod: 'الدفع عند الاستلام',
        bank: 'التحويل البنكي',
        ccp: 'الحساب الجاري البريدي',
        mobile: 'الدفع عبر الهاتف'
    };
    return names[method] || method;
}

// Validate method settings
function validateMethodSettings(method) {
    switch (method) {
        case 'cod':
            return paymentSettings.cod.areas && paymentSettings.cod.notes;
        case 'bank':
            return paymentSettings.bank.name && paymentSettings.bank.account && paymentSettings.bank.holder;
        case 'ccp':
            return paymentSettings.ccp.number && paymentSettings.ccp.key && paymentSettings.ccp.holder;
        case 'mobile':
            return paymentSettings.mobile.number && paymentSettings.mobile.holder;
        default:
            return false;
    }
}

// Export settings
function exportSettings() {
    const dataStr = JSON.stringify(paymentSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'payment-settings-' + new Date().toISOString().split('T')[0] + '.json';
    link.click();
    
    URL.revokeObjectURL(url);
    showSuccess('تم تصدير الإعدادات بنجاح');
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedSettings = JSON.parse(e.target.result);
                paymentSettings = { ...paymentSettings, ...importedSettings };
                applySettingsToUI();
                saveSettings();
                updateStatistics();
                showSuccess('تم استيراد الإعدادات بنجاح');
            } catch (error) {
                showError('خطأ في قراءة ملف الإعدادات');
            }
        };
        reader.readAsText(file);
    };
    
    input.click();
}

// Utility functions
function showLoading(message) {
    console.log('Loading:', message);
    // You can implement a loading spinner here
}

function hideLoading() {
    console.log('Loading finished');
}

function showSuccess(message) {
    // Create a temporary success alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

function showError(message) {
    // Create a temporary error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger';
    alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

console.log('💳 Payment settings script loaded');
