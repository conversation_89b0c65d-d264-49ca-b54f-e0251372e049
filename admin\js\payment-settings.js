/**
 * Payment Settings Management JavaScript
 * Handles payment method configuration and settings
 */

let paymentSettings = {
    cod: { enabled: true, fee: 0, minOrder: 1000, areas: 'جميع ولايات الجزائر', notes: 'يرجى تحضير المبلغ المطلوب عند الاستلام' },
    bank: { enabled: false, name: '', account: '', holder: '', swift: '', instructions: '' },
    ccp: { enabled: false, number: '', key: '', holder: '', instructions: '' },
    mobile: { enabled: false, number: '', app: 'mobilis-money', holder: '', instructions: '' },
    general: { currency: 'DZD', timeout: 3, thankYouMessage: 'شكراً لك على طلبك! سنتواصل معك قريباً لتأكيد الطلب وترتيب التوصيل.' }
};

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('💳 Payment settings initialized');
    loadPaymentSettings();
    updateStatistics();
    setupEventListeners();

    // Initialize shipping functionality
    setTimeout(initializeShipping, 500); // Delay to ensure DOM is ready
});

// Setup event listeners
function setupEventListeners() {
    // Auto-save on input changes
    document.addEventListener('input', function(event) {
        if (event.target.matches('input, textarea, select')) {
            autoSaveSettings();
        }
    });

    // Prevent form submission
    document.addEventListener('submit', function(event) {
        event.preventDefault();
    });
}

// Load payment settings from localStorage or API
function loadPaymentSettings() {
    try {
        // Try to load from localStorage first
        const savedSettings = localStorage.getItem('paymentSettings');
        if (savedSettings) {
            paymentSettings = { ...paymentSettings, ...JSON.parse(savedSettings) };
        }

        // Apply settings to UI
        applySettingsToUI();

        // Load from API if available
        loadFromAPI();
    } catch (error) {
        console.error('Error loading payment settings:', error);
        showError('خطأ في تحميل إعدادات الدفع');
    }
}

// Apply settings to UI elements
function applySettingsToUI() {
    // COD settings
    document.getElementById('cod-fee').value = paymentSettings.cod.fee || 0;
    document.getElementById('cod-min-order').value = paymentSettings.cod.minOrder || 1000;
    document.getElementById('cod-areas').value = paymentSettings.cod.areas || '';
    document.getElementById('cod-notes').value = paymentSettings.cod.notes || '';

    // Bank settings
    document.getElementById('bank-name').value = paymentSettings.bank.name || '';
    document.getElementById('bank-account').value = paymentSettings.bank.account || '';
    document.getElementById('bank-holder').value = paymentSettings.bank.holder || '';
    document.getElementById('bank-swift').value = paymentSettings.bank.swift || '';
    document.getElementById('bank-instructions').value = paymentSettings.bank.instructions || '';

    // CCP settings
    document.getElementById('ccp-number').value = paymentSettings.ccp.number || '';
    document.getElementById('ccp-key').value = paymentSettings.ccp.key || '';
    document.getElementById('ccp-holder').value = paymentSettings.ccp.holder || '';
    document.getElementById('ccp-instructions').value = paymentSettings.ccp.instructions || '';

    // Mobile settings
    document.getElementById('mobile-number').value = paymentSettings.mobile.number || '';
    document.getElementById('mobile-app').value = paymentSettings.mobile.app || 'mobilis-money';
    document.getElementById('mobile-holder').value = paymentSettings.mobile.holder || '';
    document.getElementById('mobile-instructions').value = paymentSettings.mobile.instructions || '';

    // General settings
    document.getElementById('default-currency').value = paymentSettings.general.currency || 'DZD';
    document.getElementById('payment-timeout').value = paymentSettings.general.timeout || 3;
    document.getElementById('thank-you-message').value = paymentSettings.general.thankYouMessage || '';

    // Update toggle switches
    updateToggleSwitch('cod', paymentSettings.cod.enabled);
    updateToggleSwitch('bank', paymentSettings.bank.enabled);
    updateToggleSwitch('ccp', paymentSettings.ccp.enabled);
    updateToggleSwitch('mobile', paymentSettings.mobile.enabled);
}

// Load settings from API
async function loadFromAPI() {
    try {
        console.log('Loading payment settings from API...');
        const response = await fetch('../php/api/payment-settings.php');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        if (data.success && data.settings) {
            paymentSettings = { ...paymentSettings, ...data.settings };
            applySettingsToUI();

            // Update statistics from API
            if (data.statistics) {
                document.getElementById('activePaymentMethods').textContent = data.statistics.active_methods || 0;
                document.getElementById('totalTransactions').textContent = data.statistics.total_transactions || 0;
                document.getElementById('successRate').textContent = (data.statistics.success_rate || 0) + '%';
            }

            console.log('Payment settings loaded from API successfully');
        }
    } catch (error) {
        console.error('Error loading from API:', error);
        console.log('Falling back to localStorage settings');
    }
}

// Toggle payment method
function togglePaymentMethod(method) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);

    const isCurrentlyActive = toggleSwitch.classList.contains('active');
    const newState = !isCurrentlyActive;

    // Update toggle switch
    updateToggleSwitch(method, newState);

    // Update settings
    paymentSettings[method].enabled = newState;

    // Save settings
    saveSettings();

    // Update statistics
    updateStatistics();

    showSuccess(`تم ${newState ? 'تفعيل' : 'إلغاء'} طريقة الدفع بنجاح`);
}

// Update toggle switch appearance
function updateToggleSwitch(method, enabled) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);

    if (enabled) {
        toggleSwitch.classList.add('active');
        config.classList.add('show');
        paymentMethod.classList.add('active');
    } else {
        toggleSwitch.classList.remove('active');
        config.classList.remove('show');
        paymentMethod.classList.remove('active');
    }
}

// Auto-save settings on input change
function autoSaveSettings() {
    // Collect all form data
    collectFormData();

    // Save to localStorage
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));

    console.log('Settings auto-saved');
}

// Collect form data into settings object
function collectFormData() {
    // COD settings
    paymentSettings.cod.fee = parseFloat(document.getElementById('cod-fee').value) || 0;
    paymentSettings.cod.minOrder = parseFloat(document.getElementById('cod-min-order').value) || 0;
    paymentSettings.cod.areas = document.getElementById('cod-areas').value;
    paymentSettings.cod.notes = document.getElementById('cod-notes').value;

    // Bank settings
    paymentSettings.bank.name = document.getElementById('bank-name').value;
    paymentSettings.bank.account = document.getElementById('bank-account').value;
    paymentSettings.bank.holder = document.getElementById('bank-holder').value;
    paymentSettings.bank.swift = document.getElementById('bank-swift').value;
    paymentSettings.bank.instructions = document.getElementById('bank-instructions').value;

    // CCP settings
    paymentSettings.ccp.number = document.getElementById('ccp-number').value;
    paymentSettings.ccp.key = document.getElementById('ccp-key').value;
    paymentSettings.ccp.holder = document.getElementById('ccp-holder').value;
    paymentSettings.ccp.instructions = document.getElementById('ccp-instructions').value;

    // Mobile settings
    paymentSettings.mobile.number = document.getElementById('mobile-number').value;
    paymentSettings.mobile.app = document.getElementById('mobile-app').value;
    paymentSettings.mobile.holder = document.getElementById('mobile-holder').value;
    paymentSettings.mobile.instructions = document.getElementById('mobile-instructions').value;

    // General settings
    paymentSettings.general.currency = document.getElementById('default-currency').value;
    paymentSettings.general.timeout = parseInt(document.getElementById('payment-timeout').value) || 3;
    paymentSettings.general.thankYouMessage = document.getElementById('thank-you-message').value;
}

// Save all settings
async function saveAllSettings() {
    try {
        showLoading('جاري حفظ الإعدادات...');

        // Collect form data
        collectFormData();

        // Save to localStorage
        localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));

        // Save to API
        try {
            const response = await fetch('../php/api/payment-settings.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(paymentSettings)
            });

            const result = await response.json();

            if (result.success) {
                showSuccess('تم حفظ جميع إعدادات الدفع بنجاح في قاعدة البيانات');
            } else {
                throw new Error(result.message || 'فشل في حفظ الإعدادات');
            }
        } catch (apiError) {
            console.error('API save error:', apiError);
            showSuccess('تم حفظ الإعدادات محلياً (قاعدة البيانات غير متاحة)');
        updateStatistics();

    } catch (error) {
        console.error('Error saving settings:', error);
        showError('خطأ في حفظ الإعدادات');
    } finally {
        hideLoading();
    }
}

// Save settings (internal function)
function saveSettings() {
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
}

// Update statistics
function updateStatistics() {
    const activeMethods = Object.values(paymentSettings).filter(method => method.enabled).length - 1; // -1 for general settings
    const totalTransactions = Math.floor(Math.random() * 1000) + 100; // Mock data
    const successRate = Math.floor(Math.random() * 20) + 80; // Mock data

    document.getElementById('activePaymentMethods').textContent = activeMethods;
    document.getElementById('totalTransactions').textContent = totalTransactions;
    document.getElementById('successRate').textContent = successRate + '%';
}

// Test payment settings
function testPaymentSettings() {
    showLoading('جاري اختبار الإعدادات...');

    setTimeout(() => {
        const activeMethods = Object.keys(paymentSettings).filter(key =>
            key !== 'general' && paymentSettings[key].enabled
        );

        if (activeMethods.length === 0) {
            showError('لا توجد طرق دفع مفعلة للاختبار');
            return;
        }

        let testResults = '🧪 نتائج الاختبار:\n\n';

        activeMethods.forEach(method => {
            const methodName = getMethodName(method);
            const isValid = validateMethodSettings(method);
            testResults += `${isValid ? '✅' : '❌'} ${methodName}: ${isValid ? 'جاهز' : 'يحتاج إعداد'}\n`;
        });

        alert(testResults);
        hideLoading();
    }, 2000);
}

// Get method display name
function getMethodName(method) {
    const names = {
        cod: 'الدفع عند الاستلام',
        bank: 'التحويل البنكي',
        ccp: 'الحساب الجاري البريدي',
        mobile: 'الدفع عبر الهاتف'
    };
    return names[method] || method;
}

// Validate method settings
function validateMethodSettings(method) {
    switch (method) {
        case 'cod':
            return paymentSettings.cod.areas && paymentSettings.cod.notes;
        case 'bank':
            return paymentSettings.bank.name && paymentSettings.bank.account && paymentSettings.bank.holder;
        case 'ccp':
            return paymentSettings.ccp.number && paymentSettings.ccp.key && paymentSettings.ccp.holder;
        case 'mobile':
            return paymentSettings.mobile.number && paymentSettings.mobile.holder;
        default:
            return false;
    }
}

// Export settings
function exportSettings() {
    const dataStr = JSON.stringify(paymentSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'payment-settings-' + new Date().toISOString().split('T')[0] + '.json';
    link.click();

    URL.revokeObjectURL(url);
    showSuccess('تم تصدير الإعدادات بنجاح');
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedSettings = JSON.parse(e.target.result);
                paymentSettings = { ...paymentSettings, ...importedSettings };
                applySettingsToUI();
                saveSettings();
                updateStatistics();
                showSuccess('تم استيراد الإعدادات بنجاح');
            } catch (error) {
                showError('خطأ في قراءة ملف الإعدادات');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

// Utility functions
function showLoading(message) {
    console.log('Loading:', message);
    // You can implement a loading spinner here
}

function hideLoading() {
    console.log('Loading finished');
}

function showSuccess(message) {
    // Create a temporary success alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 3000);
}

function showError(message) {
    // Create a temporary error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger';
    alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Shipping functionality
async function loadWilayas() {
    try {
        console.log('Loading wilayas from API...');
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=wilayas');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Wilayas API Response:', data);

        if (data.success && data.wilayas) {
            const wilayaSelect = document.getElementById('wilayaSelect');
            if (wilayaSelect) {
                wilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';

                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name} (${wilaya.zone_name} - ${wilaya.shipping_cost} دج)`;
                    wilayaSelect.appendChild(option);
                });

                console.log(`Loaded ${data.wilayas.length} wilayas`);
            }
        }
    } catch (error) {
        console.error('Error loading wilayas:', error);
        showError('فشل في تحميل قائمة الولايات');
    }
}

async function loadShippingZones() {
    try {
        console.log('Loading shipping zones...');
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=zones');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Zones API Response:', data);

        if (data.success && data.zones) {
            const zonesContainer = document.getElementById('shippingZones');
            if (zonesContainer) {
                zonesContainer.innerHTML = '';

                data.zones.forEach(zone => {
                    const zoneCard = document.createElement('div');
                    zoneCard.className = 'zone-card';
                    zoneCard.innerHTML = `
                        <div class="zone-title">${zone.zone_name}</div>
                        <div class="zone-details">${zone.wilaya_count} ولاية</div>
                        <div class="zone-cost">${zone.min_cost} - ${zone.max_cost} دج</div>
                        <div class="zone-details">حسب المسافة والوزن</div>
                    `;
                    zonesContainer.appendChild(zoneCard);
                });

                console.log(`Loaded ${data.zones.length} shipping zones`);
            }
        }
    } catch (error) {
        console.error('Error loading shipping zones:', error);
        showError('فشل في تحميل مناطق الشحن');
    }
}

async function calculateShippingCost() {
    const wilayaCode = document.getElementById('wilayaSelect').value;
    const weight = parseFloat(document.getElementById('packageWeight').value) || 1.0;

    if (!wilayaCode) {
        showError('يرجى اختيار الولاية أولاً');
        return;
    }

    if (weight < 0.1 || weight > 30) {
        showError('الوزن يجب أن يكون بين 0.1 و 30 كيلوغرام');
        return;
    }

    try {
        console.log(`Calculating shipping for wilaya: ${wilayaCode}, weight: ${weight}kg`);

        const response = await fetch(`../php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=${weight}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Shipping calculation result:', data);

        if (data.success) {
            displayShippingResult(data);
        } else {
            showError(data.message || 'فشل في حساب تكلفة الشحن');
        }
    } catch (error) {
        console.error('Error calculating shipping:', error);
        showError('حدث خطأ أثناء حساب تكلفة الشحن');
    }
}

function displayShippingResult(data) {
    const resultDiv = document.getElementById('shippingResult');
    const zoneInfo = data.zone_info;
    const costBreakdown = data.cost_breakdown;

    // Update zone information
    document.getElementById('zoneName').textContent = zoneInfo.zone_name;
    document.getElementById('wilayaName').textContent = zoneInfo.wilaya_name;
    document.getElementById('deliveryTime').textContent = zoneInfo.delivery_time;

    // Update cost breakdown
    document.getElementById('baseCost').textContent = costBreakdown.base_cost.toFixed(2);
    document.getElementById('weightDisplay').textContent = costBreakdown.weight.toFixed(1);
    document.getElementById('totalCost').textContent = costBreakdown.total_cost.toFixed(2);

    // Show/hide surcharge information
    const surchargeItem = document.getElementById('surchargeItem');
    if (costBreakdown.extra_weight > 0) {
        document.getElementById('surchargeAmount').textContent = costBreakdown.surcharge_total.toFixed(2);
        surchargeItem.style.display = 'block';
    } else {
        surchargeItem.style.display = 'none';
    }

    // Show the result
    resultDiv.style.display = 'block';
    resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Initialize shipping functionality
function initializeShipping() {
    // Load shipping data
    loadWilayas();
    loadShippingZones();

    // Load shipping management data
    loadShippingManagement();

    // Add shipping calculator event listener
    const calculateBtn = document.getElementById('calculateShipping');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateShippingCost);
    }

    // Add real-time calculation on weight change
    const weightInput = document.getElementById('packageWeight');
    if (weightInput) {
        weightInput.addEventListener('input', function() {
            const wilayaCode = document.getElementById('wilayaSelect').value;
            if (wilayaCode && this.value) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateShippingCost, 500);
            }
        });
    }

    // Add real-time calculation on wilaya change
    const wilayaSelect = document.getElementById('wilayaSelect');
    if (wilayaSelect) {
        wilayaSelect.addEventListener('change', function() {
            if (this.value) {
                calculateShippingCost();
            } else {
                document.getElementById('shippingResult').style.display = 'none';
            }
        });
    }

    // Initialize shipping management tabs
    initializeShippingManagement();
}

// Shipping Management Functions
async function loadShippingManagement() {
    try {
        // Load wilayas for custom rates dropdown
        const response = await fetch('../php/api/geographic-data.php?action=wilayas');
        const data = await response.json();

        if (data.success && data.wilayas) {
            const customWilayaSelect = document.getElementById('customWilayaSelect');
            if (customWilayaSelect) {
                customWilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';

                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name_ar} (المنطقة ${wilaya.zone_number})`;
                    customWilayaSelect.appendChild(option);
                });
            }

            // Load free shipping wilayas checkboxes
            loadFreeShippingWilayas(data.wilayas);
        }

        // Load existing custom rates
        loadCustomRates();

    } catch (error) {
        console.error('Error loading shipping management data:', error);
    }
}

function loadFreeShippingWilayas(wilayas) {
    const container = document.getElementById('freeShippingWilayas');
    if (!container) return;

    container.innerHTML = '';

    wilayas.forEach(wilaya => {
        const checkboxItem = document.createElement('div');
        checkboxItem.className = 'checkbox-item';

        checkboxItem.innerHTML = `
            <input type="checkbox" id="free_${wilaya.wilaya_code}" value="${wilaya.wilaya_code}">
            <label for="free_${wilaya.wilaya_code}">${wilaya.wilaya_name_ar}</label>
        `;

        container.appendChild(checkboxItem);
    });
}

async function loadCustomRates() {
    try {
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=custom_rates');
        const data = await response.json();

        if (data.success && data.custom_rates) {
            displayCustomRates(data.custom_rates);
        }
    } catch (error) {
        console.error('Error loading custom rates:', error);
    }
}

function displayCustomRates(customRates) {
    const container = document.getElementById('customRatesList');
    if (!container) return;

    if (customRates.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد رسوم مخصصة</p>';
        return;
    }

    container.innerHTML = '';

    customRates.forEach(rate => {
        const rateItem = document.createElement('div');
        rateItem.className = 'custom-rate-item';

        rateItem.innerHTML = `
            <div class="custom-rate-info">
                <div class="custom-rate-wilaya">${rate.wilaya_name}</div>
                <div class="custom-rate-details">مدة التوصيل: ${rate.delivery_days}</div>
            </div>
            <div class="custom-rate-cost">${rate.shipping_cost} دج</div>
            <div class="custom-rate-actions">
                <button class="btn btn-sm btn-primary" onclick="editCustomRate('${rate.location_code}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteCustomRate('${rate.location_code}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        container.appendChild(rateItem);
    });
}

function initializeShippingManagement() {
    // Free shipping toggle
    const enableFreeShipping = document.getElementById('enableFreeShipping');
    if (enableFreeShipping) {
        enableFreeShipping.addEventListener('change', function() {
            const options = document.getElementById('freeShippingOptions');
            if (options) {
                options.style.display = this.checked ? 'block' : 'none';
            }
        });
    }

    // Zone selection for zone rates
    const zoneSelect = document.getElementById('zoneSelect');
    if (zoneSelect) {
        zoneSelect.addEventListener('change', function() {
            if (this.value) {
                loadZoneWilayas(this.value);
            } else {
                document.getElementById('zoneWilayas').innerHTML = '';
            }
        });
    }
}

async function loadZoneWilayas(zoneNumber) {
    try {
        const response = await fetch(`../php/api/geographic-data.php?action=wilayas`);
        const data = await response.json();

        if (data.success && data.wilayas) {
            const zoneWilayas = data.wilayas.filter(w => w.zone_number == zoneNumber);
            displayZoneWilayas(zoneWilayas, zoneNumber);
        }
    } catch (error) {
        console.error('Error loading zone wilayas:', error);
    }
}

function displayZoneWilayas(wilayas, zoneNumber) {
    const container = document.getElementById('zoneWilayas');
    if (!container) return;

    container.innerHTML = `
        <h5>ولايات المنطقة ${zoneNumber} (${wilayas.length} ولاية)</h5>
    `;

    wilayas.forEach(wilaya => {
        const wilayaItem = document.createElement('div');
        wilayaItem.className = 'wilaya-item';

        wilayaItem.innerHTML = `
            <span class="wilaya-name">${wilaya.wilaya_name_ar}</span>
            <span class="wilaya-rate" id="rate_${wilaya.wilaya_code}">-- دج</span>
        `;

        container.appendChild(wilayaItem);

        // Load current rate for this wilaya
        loadWilayaCurrentRate(wilaya.wilaya_code);
    });
}

async function loadWilayaCurrentRate(wilayaCode) {
    try {
        const response = await fetch(`../php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=1.0`);
        const data = await response.json();

        if (data.success) {
            const rateElement = document.getElementById(`rate_${wilayaCode}`);
            if (rateElement) {
                rateElement.textContent = `${data.cost_breakdown.base_cost} دج`;
            }
        }
    } catch (error) {
        console.error(`Error loading rate for wilaya ${wilayaCode}:`, error);
    }
}

// Tab management
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Add active class to clicked button
    event.target.classList.add('active');
}

async function updateZoneRate() {
    const zoneNumber = document.getElementById('zoneSelect').value;
    const newRate = document.getElementById('newZoneRate').value;

    if (!zoneNumber || !newRate) {
        showError('يرجى اختيار المنطقة وإدخال الرسوم الجديدة');
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'update_zone_rate',
                zone_number: zoneNumber,
                new_rate: parseFloat(newRate)
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم تحديث رسوم المنطقة بنجاح');
            loadZoneWilayas(zoneNumber); // Reload to show updated rates
        } else {
            showError(data.message || 'فشل في تحديث رسوم المنطقة');
        }
    } catch (error) {
        console.error('Error updating zone rate:', error);
        showError('حدث خطأ أثناء تحديث رسوم المنطقة');
    }
}

async function setCustomRate() {
    const wilayaCode = document.getElementById('customWilayaSelect').value;
    const customRate = document.getElementById('customRate').value;
    const deliveryTime = document.getElementById('customDeliveryTime').value || '2-4 أيام عمل';

    if (!wilayaCode || !customRate) {
        showError('يرجى اختيار الولاية وإدخال الرسوم المخصصة');
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'set_custom_rate',
                wilaya_code: wilayaCode,
                shipping_cost: parseFloat(customRate),
                delivery_days: deliveryTime
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إضافة الرسوم المخصصة بنجاح');
            loadCustomRates(); // Reload custom rates list

            // Clear form
            document.getElementById('customWilayaSelect').value = '';
            document.getElementById('customRate').value = '';
            document.getElementById('customDeliveryTime').value = '';
        } else {
            showError(data.message || 'فشل في إضافة الرسوم المخصصة');
        }
    } catch (error) {
        console.error('Error setting custom rate:', error);
        showError('حدث خطأ أثناء إضافة الرسوم المخصصة');
    }
}

async function removeCustomRate() {
    const wilayaCode = document.getElementById('customWilayaSelect').value;

    if (!wilayaCode) {
        showError('يرجى اختيار الولاية');
        return;
    }

    if (!confirm('هل أنت متأكد من إزالة الرسوم المخصصة لهذه الولاية؟')) {
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'remove_custom_rate',
                wilaya_code: wilayaCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إزالة الرسوم المخصصة بنجاح');
            loadCustomRates(); // Reload custom rates list
        } else {
            showError(data.message || 'فشل في إزالة الرسوم المخصصة');
        }
    } catch (error) {
        console.error('Error removing custom rate:', error);
        showError('حدث خطأ أثناء إزالة الرسوم المخصصة');
    }
}

async function deleteCustomRate(wilayaCode) {
    if (!confirm('هل أنت متأكد من حذف هذه الرسوم المخصصة؟')) {
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'remove_custom_rate',
                wilaya_code: wilayaCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم حذف الرسوم المخصصة بنجاح');
            loadCustomRates(); // Reload custom rates list
        } else {
            showError(data.message || 'فشل في حذف الرسوم المخصصة');
        }
    } catch (error) {
        console.error('Error deleting custom rate:', error);
        showError('حدث خطأ أثناء حذف الرسوم المخصصة');
    }
}

async function saveFreeShippingSettings() {
    const enabled = document.getElementById('enableFreeShipping').checked;
    const threshold = document.getElementById('freeShippingThreshold').value;

    const selectedWilayas = [];
    document.querySelectorAll('#freeShippingWilayas input[type="checkbox"]:checked').forEach(checkbox => {
        selectedWilayas.push(checkbox.value);
    });

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'save_free_shipping',
                enabled: enabled,
                threshold: parseFloat(threshold) || 0,
                wilayas: selectedWilayas
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم حفظ إعدادات الشحن المجاني بنجاح');
        } else {
            showError(data.message || 'فشل في حفظ إعدادات الشحن المجاني');
        }
    } catch (error) {
        console.error('Error saving free shipping settings:', error);
        showError('حدث خطأ أثناء حفظ إعدادات الشحن المجاني');
    }
}

console.log('💳 Payment settings script loaded');
