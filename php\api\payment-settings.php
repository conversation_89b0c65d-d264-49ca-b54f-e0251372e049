<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

/**
 * Payment Settings API
 * Handles CRUD operations for payment method configurations
 */

function handleGet()
{
    global $conn;

    try {
        // Get all payment settings
        $stmt = $conn->prepare("
            SELECT setting_key, setting_value, setting_type, is_active, updated_at
            FROM payment_settings
            ORDER BY setting_key
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize settings by payment method
        $organizedSettings = [
            'cod' => ['enabled' => false],
            'bank' => ['enabled' => false],
            'ccp' => ['enabled' => false],
            'mobile' => ['enabled' => false],
            'general' => []
        ];

        foreach ($settings as $setting) {
            $parts = explode('_', $setting['setting_key'], 2);
            $method = $parts[0];
            $key = isset($parts[1]) ? $parts[1] : 'enabled';

            if (isset($organizedSettings[$method])) {
                $value = $setting['setting_value'];

                // Convert value based on type
                switch ($setting['setting_type']) {
                    case 'boolean':
                        $value = (bool)$value;
                        break;
                    case 'integer':
                        $value = (int)$value;
                        break;
                    case 'float':
                        $value = (float)$value;
                        break;
                    default:
                        $value = (string)$value;
                }

                if ($key === 'enabled') {
                    $organizedSettings[$method]['enabled'] = (bool)$setting['is_active'];
                } else {
                    $organizedSettings[$method][$key] = $value;
                }
            }
        }

        // Get payment statistics
        $stats = getPaymentStatistics();

        echo json_encode([
            'success' => true,
            'settings' => $organizedSettings,
            'statistics' => $stats,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (PDOException $e) {
        error_log('Database error in payment settings GET: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    } catch (Exception $e) {
        error_log('Error in payment settings GET: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred',
            'error_code' => 500
        ]);
    }
}

function handlePost()
{
    global $conn;

    try {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data) {
            throw new Exception('Invalid JSON data provided');
        }

        $conn->beginTransaction();

        // Process each payment method
        foreach ($data as $method => $settings) {
            if (!is_array($settings)) continue;

            foreach ($settings as $key => $value) {
                $settingKey = $method . '_' . $key;

                // Determine setting type
                $settingType = 'string';
                if (is_bool($value)) {
                    $settingType = 'boolean';
                    $value = $value ? 1 : 0;
                } elseif (is_int($value)) {
                    $settingType = 'integer';
                } elseif (is_float($value)) {
                    $settingType = 'float';
                }

                // Check if setting exists
                $stmt = $conn->prepare("SELECT id FROM payment_settings WHERE setting_key = ?");
                $stmt->execute([$settingKey]);
                $existing = $stmt->fetch();

                if ($existing) {
                    // Update existing setting
                    $stmt = $conn->prepare("
                        UPDATE payment_settings
                        SET setting_value = ?, setting_type = ?, updated_at = NOW()
                        WHERE setting_key = ?
                    ");
                    $stmt->execute([$value, $settingType, $settingKey]);
                } else {
                    // Insert new setting
                    $stmt = $conn->prepare("
                        INSERT INTO payment_settings (setting_key, setting_value, setting_type, is_active, created_at, updated_at)
                        VALUES (?, ?, ?, 1, NOW(), NOW())
                    ");
                    $stmt->execute([$settingKey, $value, $settingType]);
                }

                // Update is_active for enabled settings
                if ($key === 'enabled') {
                    $stmt = $conn->prepare("
                        UPDATE payment_settings
                        SET is_active = ?
                        WHERE setting_key LIKE ?
                    ");
                    $stmt->execute([$value, $method . '_%']);
                }
            }
        }

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Payment settings saved successfully',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Database error in payment settings POST: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Error in payment settings POST: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'error_code' => 500
        ]);
    }
}

function getPaymentStatistics()
{
    global $conn;

    try {
        // Count active payment methods
        $stmt = $conn->prepare("
            SELECT COUNT(DISTINCT SUBSTRING_INDEX(setting_key, '_', 1)) as active_methods
            FROM payment_settings
            WHERE is_active = 1 AND setting_key LIKE '%_enabled'
        ");
        $stmt->execute();
        $activeMethods = $stmt->fetchColumn() ?: 0;

        // Mock transaction data (replace with real data when available)
        $totalTransactions = rand(100, 1000);
        $successRate = rand(85, 98);

        return [
            'active_methods' => (int)$activeMethods,
            'total_transactions' => $totalTransactions,
            'success_rate' => $successRate
        ];
    } catch (Exception $e) {
        error_log('Error getting payment statistics: ' . $e->getMessage());
        return [
            'active_methods' => 0,
            'total_transactions' => 0,
            'success_rate' => 0
        ];
    }
}

function createPaymentSettingsTable()
{
    global $conn;

    try {
        // Create payment_settings table
        $sql = "
        CREATE TABLE IF NOT EXISTS payment_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_setting_key (setting_key),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->exec($sql);

        // Create shipping_zones table
        $shippingZonesSql = "
        CREATE TABLE IF NOT EXISTS shipping_zones (
            id INT AUTO_INCREMENT PRIMARY KEY,
            zone_name VARCHAR(50) NOT NULL,
            zone_number INT NOT NULL,
            wilaya_name VARCHAR(100) NOT NULL,
            wilaya_code VARCHAR(10) NOT NULL,
            shipping_cost DECIMAL(10,2) NOT NULL,
            weight_limit DECIMAL(5,2) DEFAULT 5.00,
            surcharge_per_kg DECIMAL(10,2) DEFAULT 0.00,
            delivery_days VARCHAR(20) DEFAULT '2-4 أيام',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_zone_number (zone_number),
            INDEX idx_wilaya_code (wilaya_code),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->exec($shippingZonesSql);

        // Create shipping_settings table
        $shippingSettingsSql = "
        CREATE TABLE IF NOT EXISTS shipping_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_setting_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->exec($shippingSettingsSql);

        // Initialize shipping zones with Yalidine Express rates
        initializeShippingZones();

        // Initialize shipping settings
        initializeShippingSettings();

        return true;
    } catch (PDOException $e) {
        error_log('Error creating shipping tables: ' . $e->getMessage());
        return false;
    }
}

function initializeShippingZones()
{
    global $conn;

    try {
        // Check if zones are already initialized
        $stmt = $conn->prepare("SELECT COUNT(*) FROM shipping_zones");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        if ($count > 0) {
            return; // Already initialized
        }

        // Yalidine Express shipping zones data
        $zones = [
            // Zone 1 - 450 DA
            ['Zone 1', 1, 'Blida', '09', 450.00, 5.00, 50.00, '1-2 أيام عمل'],
            ['Zone 1', 1, 'Tipaza', '42', 450.00, 5.00, 50.00, '1-2 أيام عمل'],
            ['Zone 1', 1, 'Boumerdès', '35', 450.00, 5.00, 50.00, '1-2 أيام عمل'],

            // Zone 2 - 550-750 DA
            ['Zone 2', 2, 'Bouira', '10', 550.00, 5.00, 75.00, '2-3 أيام عمل'],
            ['Zone 2', 2, 'Tizi Ouzou', '15', 550.00, 5.00, 75.00, '2-3 أيام عمل'],
            ['Zone 2', 2, 'Béjaïa', '06', 650.00, 5.00, 75.00, '2-3 أيام عمل'],
            ['Zone 2', 2, 'Sétif', '19', 650.00, 5.00, 75.00, '2-3 أيام عمل'],
            ['Zone 2', 2, 'Constantine', '25', 750.00, 5.00, 75.00, '2-3 أيام عمل'],
            ['Zone 2', 2, 'Annaba', '23', 750.00, 5.00, 75.00, '2-3 أيام عمل'],

            // Zone 3 - 550-750 DA
            ['Zone 3', 3, 'Médéa', '26', 550.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 3', 3, 'Djelfa', '17', 650.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 3', 3, 'M\'Sila', '28', 650.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 3', 3, 'Batna', '05', 750.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 3', 3, 'Biskra', '07', 750.00, 5.00, 75.00, '2-4 أيام عمل'],

            // Zone 4 - 550-750 DA
            ['Zone 4', 4, 'Chlef', '02', 550.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 4', 4, 'Ain Defla', '44', 550.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 4', 4, 'Relizane', '48', 650.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 4', 4, 'Mascara', '29', 650.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 4', 4, 'Oran', '31', 750.00, 5.00, 75.00, '2-4 أيام عمل'],
            ['Zone 4', 4, 'Tlemcen', '13', 750.00, 5.00, 75.00, '2-4 أيام عمل'],

            // Zone 5 - 850-1800 DA
            ['Zone 5', 5, 'Laghouat', '03', 850.00, 5.00, 100.00, '3-7 أيام عمل'],
            ['Zone 5', 5, 'El Oued', '39', 1200.00, 5.00, 150.00, '4-7 أيام عمل'],
            ['Zone 5', 5, 'Ghardaïa', '47', 1200.00, 5.00, 150.00, '4-7 أيام عمل'],
            ['Zone 5', 5, 'Ouargla', '30', 1400.00, 5.00, 200.00, '5-7 أيام عمل'],
            ['Zone 5', 5, 'Adrar', '01', 1600.00, 5.00, 250.00, '5-10 أيام عمل'],
            ['Zone 5', 5, 'Tamanrasset', '11', 1800.00, 5.00, 300.00, '7-10 أيام عمل'],
            ['Zone 5', 5, 'Illizi', '33', 1800.00, 5.00, 300.00, '7-10 أيام عمل'],
            ['Zone 5', 5, 'Tindouf', '37', 1800.00, 5.00, 300.00, '7-10 أيام عمل']
        ];

        $stmt = $conn->prepare("
            INSERT INTO shipping_zones (zone_name, zone_number, wilaya_name, wilaya_code, shipping_cost, weight_limit, surcharge_per_kg, delivery_days)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($zones as $zone) {
            $stmt->execute($zone);
        }

        error_log('Shipping zones initialized successfully');
    } catch (PDOException $e) {
        error_log('Error initializing shipping zones: ' . $e->getMessage());
    }
}

function initializeShippingSettings()
{
    global $conn;

    try {
        // Check if settings are already initialized
        $stmt = $conn->prepare("SELECT COUNT(*) FROM shipping_settings");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        if ($count > 0) {
            return; // Already initialized
        }

        $settings = [
            ['company_name', 'Yalidine Express', 'string', 'اسم شركة الشحن'],
            ['base_location', 'Bordj El Kiffan, Algiers', 'string', 'موقع المتجر الأساسي'],
            ['max_weight', '30', 'float', 'الحد الأقصى للوزن (كيلوغرام)'],
            ['min_weight', '0.1', 'float', 'الحد الأدنى للوزن (كيلوغرام)'],
            ['free_shipping_threshold', '5000', 'float', 'حد الشحن المجاني (دينار)'],
            ['weight_unit', 'kg', 'string', 'وحدة الوزن'],
            ['currency', 'DA', 'string', 'العملة'],
            ['shipping_enabled', '1', 'boolean', 'تفعيل الشحن'],
            ['express_delivery', '1', 'boolean', 'التوصيل السريع متاح'],
            ['insurance_rate', '2', 'float', 'نسبة التأمين (%)']
        ];

        $stmt = $conn->prepare("
            INSERT INTO shipping_settings (setting_key, setting_value, setting_type, description)
            VALUES (?, ?, ?, ?)
        ");

        foreach ($settings as $setting) {
            $stmt->execute($setting);
        }

        error_log('Shipping settings initialized successfully');
    } catch (PDOException $e) {
        error_log('Error initializing shipping settings: ' . $e->getMessage());
    }
}

// Initialize table if it doesn't exist
createPaymentSettingsTable();

function calculateShipping($wilayaCode, $weight = 1.0)
{
    global $conn;

    try {
        // Get shipping zone for the wilaya
        $stmt = $conn->prepare("
            SELECT zone_name, zone_number, wilaya_name, shipping_cost, weight_limit, surcharge_per_kg, delivery_days
            FROM shipping_zones
            WHERE wilaya_code = ? AND is_active = 1
        ");
        $stmt->execute([$wilayaCode]);
        $zone = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$zone) {
            return [
                'success' => false,
                'message' => 'منطقة الشحن غير متاحة لهذه الولاية',
                'wilaya_code' => $wilayaCode
            ];
        }

        $baseCost = (float)$zone['shipping_cost'];
        $weightLimit = (float)$zone['weight_limit'];
        $surchargePerKg = (float)$zone['surcharge_per_kg'];
        $totalCost = $baseCost;

        // Calculate surcharge for weight over limit
        if ($weight > $weightLimit) {
            $extraWeight = $weight - $weightLimit;
            $surcharge = $extraWeight * $surchargePerKg;
            $totalCost += $surcharge;
        }

        // Get free shipping threshold
        $stmt = $conn->prepare("SELECT setting_value FROM shipping_settings WHERE setting_key = 'free_shipping_threshold'");
        $stmt->execute();
        $freeShippingThreshold = (float)($stmt->fetchColumn() ?: 0);

        return [
            'success' => true,
            'zone_info' => [
                'zone_name' => $zone['zone_name'],
                'zone_number' => $zone['zone_number'],
                'wilaya_name' => $zone['wilaya_name'],
                'delivery_time' => $zone['delivery_days']
            ],
            'cost_breakdown' => [
                'base_cost' => $baseCost,
                'weight' => $weight,
                'weight_limit' => $weightLimit,
                'extra_weight' => max(0, $weight - $weightLimit),
                'surcharge_per_kg' => $surchargePerKg,
                'surcharge_total' => max(0, ($weight - $weightLimit) * $surchargePerKg),
                'total_cost' => $totalCost,
                'currency' => 'DA'
            ],
            'free_shipping' => [
                'threshold' => $freeShippingThreshold,
                'eligible' => false // Will be calculated based on order total
            ]
        ];
    } catch (PDOException $e) {
        error_log('Database error in calculateShipping: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'خطأ في حساب تكلفة الشحن',
            'error_code' => $e->getCode()
        ];
    }
}

function getAllWilayas()
{
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT DISTINCT wilaya_code, wilaya_name, zone_name, zone_number, shipping_cost, delivery_days
            FROM shipping_zones
            WHERE is_active = 1
            ORDER BY zone_number, wilaya_name
        ");
        $stmt->execute();
        $wilayas = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'wilayas' => $wilayas,
            'total_count' => count($wilayas)
        ];
    } catch (PDOException $e) {
        error_log('Database error in getAllWilayas: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'خطأ في جلب بيانات الولايات',
            'error_code' => $e->getCode()
        ];
    }
}

function handleShippingRequest()
{
    global $conn;

    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'calculate':
            $wilayaCode = $_GET['wilaya_code'] ?? '';
            $weight = (float)($_GET['weight'] ?? 1.0);

            if (empty($wilayaCode)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'كود الولاية مطلوب']);
                return;
            }

            $result = calculateShipping($wilayaCode, $weight);
            echo json_encode($result);
            break;

        case 'wilayas':
            $result = getAllWilayas();
            echo json_encode($result);
            break;

        case 'zones':
            try {
                $stmt = $conn->prepare("
                    SELECT zone_number, zone_name, COUNT(*) as wilaya_count,
                           MIN(shipping_cost) as min_cost, MAX(shipping_cost) as max_cost
                    FROM shipping_zones
                    WHERE is_active = 1
                    GROUP BY zone_number, zone_name
                    ORDER BY zone_number
                ");
                $stmt->execute();
                $zones = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'zones' => $zones
                ]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'خطأ في جلب بيانات المناطق']);
            }
            break;

        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
}

// Route requests
$method = $_SERVER['REQUEST_METHOD'];

// Check if this is a shipping-related request
if (isset($_GET['module']) && $_GET['module'] === 'shipping') {
    handleShippingRequest();
    exit;
}

switch ($method) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
    case 'PUT':
        handlePost();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
