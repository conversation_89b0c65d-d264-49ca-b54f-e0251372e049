إضافة صفحة هبوط جديدة
issue bouton "حفظ" و إلغاء



Navigation item clicked:
<li data-section="books">
admin.js:419:21
Switching to section: books admin.js:433:21
Loading books... admin.js:464:33
📦 Calling loadProducts function... admin.js:466:21
📦 Loading products... admin.js:681:17
API Response:
Object { success: true, products: (5) […] }
admin.js:687:17
Products data:
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
admin.js:702:17
Found 5 products admin.js:703:17
Adding view more link for product:
Object { id: 18, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=18" }
admin.js:1316:13
Adding view more link for product:
Object { id: 18, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=18" }
admin.js:1316:13
Adding view more link for product:
Object { id: 18, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=18" }
admin.js:1316:13
Adding view more link for product:
Object { id: 19, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 20, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Navigation item clicked:
<li class="" data-section="landingPages">
admin.js:419:21
Switching to section: landingPages admin.js:433:21
Loading landing pages... admin.js:473:33
Landing Pages Manager is available admin.js:476:37
Landing Pages Manager already initialized, refreshing data... admin.js:482:41
🔄 Loading landing pages... landing-pages.js:536:17
📡 Making API call to: ../php/api/landing-pages.php?_t=1752170075143 landing-pages.js:541:21
📡 Raw API Response:
Object { success: true, data: (3) […] }
landing-pages.js:544:21
📡 Response type: object landing-pages.js:545:21
📡 Response keys:
Array [ "success", "data" ]
landing-pages.js:546:21
📋 Landing pages data:
Array(3) [ {…}, {…}, {…} ]
landing-pages.js:549:21
📋 Pages type: object landing-pages.js:550:21
📋 Is array: true landing-pages.js:551:21
📋 Pages length: 3 landing-pages.js:552:21
🎨 Displaying landing pages:
Array(3) [ {…}, {…}, {…} ]
landing-pages.js:572:17
🎨 Pages parameter type: object landing-pages.js:573:17
🎨 Pages parameter value:
Array(3) [ {…}, {…}, {…} ]
landing-pages.js:574:17
🎨 Container found: true landing-pages.js:577:17
🎨 Container element:
<div id="landingPagesContainer" style="margin-top: 20px; opacity: 1;">
landing-pages.js:578:17
📄 Displaying 3 landing pages landing-pages.js:593:17
🖱️ Add landing page button clicked! landing-pages.js:306:25
Button element:
<button id="addLandingPageBtn" class="action-button">
landing-pages.js:307:25
Opening modal... landing-pages.js:308:25
🚀 Opening modal... landing-pages.js:629:17
Landing pages section display: block landing-pages.js:650:25
🔄 Modal reset to step 1 landing-pages.js:295:17
Refreshing product selection... landing-pages.js:412:17
📦 Loading active products for landing page selection... landing-pages.js:355:21
Initializing TinyMCE for landing pages modal landing-pages.js:801:17
No TinyMCE editors found to clean up landing-pages.js:983:25
TinyMCE initialization attempt 1/5 landing-pages.js:812:25
TinyMCE initialization successful landing-pages.js:938:29
✅ Modal opened successfully landing-pages.js:691:21
Modal styles:
Object { display: "block", opacity: "1", visibility: "visible", zIndex: "9999", position: "fixed" }
landing-pages.js:694:21
🎉 TEST: Button click detected! landing-pages.js:485:25
Found 5 active products out of 5 total products landing-pages.js:373:21
Active products loaded successfully landing-pages.js:392:21
Navigation item clicked:
<li data-section="settings">
admin.js:419:21
Switching to section: settings admin.js:433:21
Loading settings... admin.js:491:33
Loading settings... admin.js:217:17
Starting TinyMCE initialization... admin.js:910:13
Added tinymce class to: productDescription admin.js:929:17
TinyMCE initialization - Found textareas: 1 admin.js:931:13
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
Settings response text: {"success":true,"settings":{"store_name":"Mossaab Store","store_description":"Your one-stop shop for books and electronics","contact_email":"<EMAIL>","phone_number":"+213 000000000","address":"Algeria","shipping_policy":"Standard shipping within 3-5 business days","return_policy":"30-day return policy for unused items"}} admin.js:225:17
Parsed settings data:
Object { success: true, settings: {…} }
admin.js:241:17
Final settings object:
Object { store_name: "Mossaab Store", store_description: "Your one-stop shop for books and electronics", contact_email: "<EMAIL>", phone_number: "+213 000000000", address: "Algeria", shipping_policy: "Standard shipping within 3-5 business days", return_policy: "30-day return policy for unused items" }
admin.js:256:17
Settings loaded successfully admin.js:277:17
TinyMCE editor initialized: storeAddress admin.js:957:29
✅ Template selected:
Object { id: "home", name: "قالب الأجهزة المنزلية", description: "قالب مخصص للأجهزة المنزلية والمطبخ", icon: "fas fa-home", preview: "/images/template-home-preview.jpg", content: {…} }
landing-pages.js:158:21
Applying template:
Object { titre: "راحة وعملية في منزلك - {product_title}", contenu_droit: '\n                <h3>🏠 لماذا هذا الجهاز ضروري لمنزلك؟</h3>\n                <ul>\n                    <li><strong>تقنية حديثة:</strong> أحدث التقنيات لأفضل أداء</li>\n                    <li><strong>سهولة الاستخدام:</strong> تشغيل بسيط ومريح</li>\n                    <li><strong>توفير الوقت:</strong> ينجز المهام بسرعة وكفاءة</li>\n                    <li><strong>تصميم أنيق:</strong> يناسب ديكور منزلك</li>\n                    <li><strong>كفاءة في الطاقة:</strong> استهلاك منخفض للكهرباء</li>\n                </ul>\n\n                <h3>⚡ المواصفات التقنية:</h3>\n                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">\n                    <tr style="background: #f8f9fa;">\n                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>القوة</strong></td>\n                        <td style="padding: 10px; border: 1px solid #ddd;">قوة عالية وأداء ممتاز</td>\n                    </tr>\n                    <tr>\n                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>الضمان</strong></td>\n                        <td style="padding: 10px; border: 1px solid #ddd;">ضمان شامل لمدة سنة</td>\n                    </tr>\n                    <tr style="background: #f8f9fa;">\n                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>الصيانة</strong></td>\n                        <td style="padding: 10px; border: 1px solid #ddd;">سهل التنظيف والصيانة</td>\n                    </tr>\n                </table>', contenu_gauche: '\n                <h3>🔧 خدمة ما بعد البيع</h3>\n                <p>نحن نقدم خدمة عملاء متميزة وصيانة دورية لضمان أفضل أداء لجهازك على المدى الطويل.</p>\n\n                <h3>🌟 آراء العملاء</h3>\n                <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">\n                    "جهاز رائع! وفر علي الكثير من الوقت والجهد في المطبخ."\n                    <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة محمد، ربة منزل</cite>\n                </blockquote>\n\n                <h3>🎯 مثالي للاستخدامات التالية:</h3>\n                <p>• الاستخدام اليومي في المطبخ<br>\n                • تحضير الوجبات السريعة<br>\n                • المناسبات والضيافة<br>\n                • توفير الوقت والجهد</p>', image_position: "right", text_position: "left" }
landing-pages.js:205:17
Initializing TinyMCE for landing pages modal landing-pages.js:801:17
No TinyMCE editors found to clean up landing-pages.js:983:25
TinyMCE initialization attempt 1/5 landing-pages.js:812:25
📝 Moved to content step landing-pages.js:181:17
TinyMCE initialization successful landing-pages.js:938:29
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow tinymce.min.js:4:18466
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow tinymce.min.js:4:18466
Content set successfully for rightContent landing-pages.js:246:37
Content set successfully for leftContent landing-pages.js:246:37
✅ Template applied to form successfully landing-pages.js:265:21
📤 Submitting form... Create mode landing-pages.js:1173:17
🌐 Added 0 URL images to form data landing-pages.js:1212:17
📤 Sending form data to API... landing-pages.js:1215:21
Content changed in editor: rightContent admin.js:962:29
Current content: <h3>🏠 لماذا هذا الجهاز ضروري لمنزلك؟</h3>
<ul>
<li><strong>تقنية حديثة:</strong> أحدث التقنيات لأفضل أداء</li>
<li><strong>سهولة الاستخدام:</strong> تشغيل بسيط ومريح</li>
<li><strong>توفير الوقت:</strong> ينجز المهام بسرعة وكفاءة</li>
<li><strong>تصميم أنيق:</strong> يناسب ديكور منزلك</li>
<li><strong>كفاءة في الطاقة:</strong> استهلاك منخفض للكهرباء</li>
</ul>
<h3>⚡ المواصفات التقنية:</h3>
<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
<tbody>
<tr style="background: #f8f9fa;">
<td style="padding: 10px; border: 1px solid #ddd;"><strong>القوة</strong></td>
<td style="padding: 10px; border: 1px solid #ddd;">قوة عالية وأداء ممتاز</td>
</tr>
<tr>
<td style="padding: 10px; border: 1px solid #ddd;"><strong>الضمان</strong></td>
<td style="padding: 10px; border: 1px solid #ddd;">ضمان شامل لمدة سنة</td>
</tr>
<tr style="background: #f8f9fa;">
<td style="padding: 10px; border: 1px solid #ddd;"><strong>الصيانة</strong></td>
<td style="padding: 10px; border: 1px solid #ddd;">سهل التنظيف والصيانة</td>
</tr>
</tbody>
</table> admin.js:963:29
Content changed in editor: leftContent admin.js:962:29
Current content: <h3>🔧 خدمة ما بعد البيع</h3>
<p>نحن نقدم خدمة عملاء متميزة وصيانة دورية لضمان أفضل أداء لجهازك على المدى الطويل.</p>
<h3>🌟 آراء العملاء</h3>
<blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">"جهاز رائع! وفر علي الكثير من الوقت والجهد في المطبخ." <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة محمد، ربة منزل</cite></blockquote>
<h3>🎯 مثالي للاستخدامات التالية:</h3>
<p>&bull; الاستخدام اليومي في المطبخ<br>&bull; تحضير الوجبات السريعة<br>&bull; المناسبات والضيافة<br>&bull; توفير الوقت والجهد</p> admin.js:963:29
📥 Response status: 200 landing-pages.js:1222:21
📥 Raw response: {"success":true,"message":"Landing page created successfully"} landing-pages.js:1231:21
✅ Landing page operation successful landing-pages.js:1253:25
🔄 Reloading landing pages after successful operation... landing-pages.js:1256:25
🔄 Loading landing pages... landing-pages.js:536:17
📡 Making API call to: ../php/api/landing-pages.php?_t=1752170108074 landing-pages.js:541:21
📡 Raw API Response:
Object { success: true, data: (4) […] }
landing-pages.js:544:21
📡 Response type: object landing-pages.js:545:21
📡 Response keys:
Array [ "success", "data" ]
landing-pages.js:546:21
📋 Landing pages data:
Array(4) [ {…}, {…}, {…}, {…} ]
landing-pages.js:549:21
📋 Pages type: object landing-pages.js:550:21
📋 Is array: true landing-pages.js:551:21
📋 Pages length: 4 landing-pages.js:552:21
🎨 Displaying landing pages:
Array(4) [ {…}, {…}, {…}, {…} ]
landing-pages.js:572:17
🎨 Pages parameter type: object landing-pages.js:573:17
🎨 Pages parameter value:
Array(4) [ {…}, {…}, {…}, {…} ]
landing-pages.js:574:17
🎨 Container found: true landing-pages.js:577:17
🎨 Container element:
<div id="landingPagesContainer" style="margin-top: 20px; opacity: 1;">
landing-pages.js:578:17
📄 Displaying 4 landing pages landing-pages.js:593:17
🚪 Closing modal... landing-pages.js:708:17
Error closing modal: TypeError: can't convert undefined to object
    closeModal http://localhost:8000/admin/js/landing-pages.js:730
    handleSubmit http://localhost:8000/admin/js/landing-pages.js:1261
    setTimeout handler*handleSubmit http://localhost:8000/admin/js/landing-pages.js:1260
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    init http://localhost:8000/admin/js/landing-pages.js:90
    <anonymous> http://localhost:8000/admin/js/landing-pages.js:1804
    EventListener.handleEvent* http://localhost:8000/admin/js/landing-pages.js:1801
<anonymous code>:1:145535
La ressource média http://localhost:8000/assets/notification.mp3 n’a pu être décodée. index.html
Notification sound not available: The media resource indicated by the src attribute or assigned media provider object was not suitable. admin.js:110:33
La ressource média http://localhost:8000/assets/notification.mp3 n’a pu être décodée. index.html
Notification sound not available: The media resource indicated by the src attribute or assigned media provider object was not suitable. admin.js:110:33
La ressource multimédia http://localhost:8000/assets/notification.mp3 n’a pas pu être décodée, erreur : Error Code: NS_ERROR_DOM_MEDIA_METADATA_ERR (0x806e0006) index.html
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
TypeError: can't access property "rangeCount", selection is null contextmenuhlpr.js:8:9
MouseEvent.mozInputSource est obsolète. Veuillez utiliser plutôt PointerEvent.pointerType. tinymce.min.js:4:51163
📤 Submitting form... Create mode landing-pages.js:1173:17
🌐 Added 0 URL images to form data landing-pages.js:1212:17
📤 Sending form data to API... landing-pages.js:1215:21
📥 Response status: 200 landing-pages.js:1222:21
📥 Raw response: {"success":true,"message":"Landing page created successfully"} landing-pages.js:1231:21
✅ Landing page operation successful landing-pages.js:1253:25
🔄 Reloading landing pages after successful operation... landing-pages.js:1256:25
🔄 Loading landing pages... landing-pages.js:536:17
📡 Making API call to: ../php/api/landing-pages.php?_t=1752170179077 landing-pages.js:541:21
📡 Raw API Response:
Object { success: true, data: (5) […] }
landing-pages.js:544:21
📡 Response type: object landing-pages.js:545:21
📡 Response keys:
Array [ "success", "data" ]
landing-pages.js:546:21
📋 Landing pages data:
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
landing-pages.js:549:21
📋 Pages type: object landing-pages.js:550:21
📋 Is array: true landing-pages.js:551:21
📋 Pages length: 5 landing-pages.js:552:21
🎨 Displaying landing pages:
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
landing-pages.js:572:17
🎨 Pages parameter type: object landing-pages.js:573:17
🎨 Pages parameter value:
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
landing-pages.js:574:17
🎨 Container found: true landing-pages.js:577:17
🎨 Container element:
<div id="landingPagesContainer" style="margin-top: 20px; opacity: 1;">
landing-pages.js:578:17
📄 Displaying 5 landing pages landing-pages.js:593:17
🚪 Closing modal... landing-pages.js:708:17
Error closing modal: TypeError: can't convert undefined to object
    closeModal http://localhost:8000/admin/js/landing-pages.js:730
    handleSubmit http://localhost:8000/admin/js/landing-pages.js:1261
    setTimeout handler*handleSubmit http://localhost:8000/admin/js/landing-pages.js:1260
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    init http://localhost:8000/admin/js/landing-pages.js:90
    <anonymous> http://localhost:8000/admin/js/landing-pages.js:1804
    EventListener.handleEvent* http://localhost:8000/admin/js/landing-pages.js:1801
<anonymous code>:1:145535
La ressource média http://localhost:8000/assets/notification.mp3 n’a pu être décodée. index.html
Notification sound not available: The media resource indicated by the src attribute or assigned media provider object was not suitable. admin.js:110:33
La ressource média http://localhost:8000/assets/notification.mp3 n’a pu être décodée. index.html
Notification sound not available: The media resource indicated by the src attribute or assigned media provider object was not suitable. admin.js:110:33
La ressource multimédia http://localhost:8000/assets/notification.mp3 n’a pas pu être décodée, erreur : Error Code: NS_ERROR_DOM_MEDIA_METADATA_ERR (0x806e0006) index.html
