Uncaught SyntaxError: missing = in const declaration landing-pages.js:972:5
downloadable font: Glyph bbox was incorrect (glyph ids 1 2 3 4 5 8 9 10 11 12 13 14 16 17 19 22 24 28 32 34 35 38 39 40 43 44 45 46 47 50 51 52 53 55 56 58 60 61 62 64 67 68 70 71 72 73 74 78 79 80 81 83 90 96 *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** *********** 503 508 509 513 515 516 525 527 528 532 535 541 542 543 549 550 551 552 554 555 556 558 560 569 571 593 602 603 604 607 608 609 614 615 617 618 623 626 627 643 644 645 647 650 651 654 655 656 657 662 663 664 665 670 671 672 674 675 679 680 681 682 683 698 699 708 712 714 717 718 729 730 732 735 736 739 746 747 752 761 762 767 774 776 777 778 779 788 789 790 794 796 798 799 800 801 803 804 806 826 828 829 831 835 836 838 839 840 841 842 843 844 845 848 849 856 857 861 862 863 871 873 874 880 882 892 895 900 908 911 913 925 928 929 930 933 936 937 938 941 942 943 944 945 948 949 950 952 958 960 961 962 964 966 967 969 973 974 978 979 980 981 982 989 998 1000 1001 1005 1006 1008 1009 1011 1012 1013 1016 1020 1026 1027 1031 1036 1037 1042 1045 1048 1050 1052 1053 1057 1058 1060 1063 1072 1073 1076 1084 1087 1099 1104 1110 1111 1112 1116 1117 1121 1122 1124 1131 1136 1140 1141 1142 1147 1148 1151 1157 1163 1167 1168 1170 1177 1186 1187 1193 1196 1199 1200 1201 1204 1205 1208 1211 1212 1217 1218 1220 1224 1226 1228 1230 1231 1232 1233 1235 1236 1237 1238 1243 1246 1247 1249 1251 1256 1258 1259 1260 1261 1265 1268 1269 1271 1272 1273 1275 1276 1279 1285 1289 1290 1291 1292 1296 1297 1303 1304 1305 1309 1310 1311 1312 1317 1319 1320 1324 1325 1328 1329 1330 1331 1334 1335 1337 1339 1341 1343 1356 1357 1363 1369 1371 1372 1375 1376 1377 1382 1384 1387) (font-family: "Font Awesome 6 Free" style:normal weight:900 stretch:100 src index:0) source: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2
Starting TinyMCE initialization... admin.js:910:13
Added tinymce class to: productDescription admin.js:929:17
TinyMCE initialization - Found textareas: 1 admin.js:931:13
DOM Content Loaded - Starting initialization... admin.js:1761:13
Initializing navigation... admin.js:403:13
Found navigation items: 6 admin.js:407:13
Adding click listener to item 0:
<li class="active" data-section="dashboard">
admin.js:415:17
Adding click listener to item 1:
<li data-section="books">
admin.js:415:17
Adding click listener to item 2:
<li data-section="orders">
admin.js:415:17
Adding click listener to item 3:
<li data-section="landingPages">
admin.js:415:17
Adding click listener to item 4:
<li data-section="settings">
admin.js:415:17
Adding click listener to item 5:
<li id="logoutBtn">
admin.js:415:17
Navigation initialization complete admin.js:511:13
Initializing form handlers... admin.js:983:13
All requirements met, proceeding with form initialization... admin.js:1007:13
Starting TinyMCE initialization... admin.js:910:13
Added tinymce class to: productDescription admin.js:929:17
TinyMCE initialization - Found textareas: 1 admin.js:931:13
Initialization complete admin.js:1780:13
MouseEvent.mozInputSource est obsolète. Veuillez utiliser plutôt PointerEvent.pointerType. tinymce.min.js:4:51163
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
Erreur dans les liens source : JSON.parse: unexpected character at line 1 column 1 of the JSON data
URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
TypeError: can't access property "rangeCount", selection is null 3 contextmenuhlpr.js:8:9
Dashboard data loaded:
Object { products: {…}, orders: {…}, sales: {…}, landing_pages: {…}, books: {…}, charts: {…}, recent_orders: [] }
admin.js:617:17
✅ Dashboard statistics updated successfully admin.js:658:17
TinyMCE editor initialized: rightContent admin.js:957:29
TinyMCE editor initialized: leftContent admin.js:957:29
TinyMCE editor initialized: storeAddress admin.js:957:29
TinyMCE editor initialized: productDescription admin.js:957:29
Store settings loaded successfully:
Object { store_name: "متجر مصعب", store_description: "متجر إلكتروني متخصص", contact_email: "<EMAIL>", contact_phone: "+*********** 789", address: "الجزائر", currency: "DZD", tax_rate: 0, shipping_cost: 0, free_shipping_threshold: 5000 }
admin.js:1360:21
Store settings response format:
Object { success: true, data: {…} }
tinymce-config.js:18:25
Error loading store settings: Error: Failed to load store settings
    loadStoreSettings http://localhost:8000/admin/js/admin.js:1391
    async* http://localhost:8000/admin/js/admin.js:1771
    EventListener.handleEvent* http://localhost:8000/admin/js/admin.js:1753
<anonymous code>:1:145535
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
TypeError: can't access property "rangeCount", selection is null 6 contextmenuhlpr.js:8:9





produit form save issue :
Navigation item clicked:
<li class="" data-section="books">
admin.js:419:21
Switching to section: books admin.js:433:21
Loading books... admin.js:464:33
📦 Calling loadProducts function... admin.js:466:21
📦 Loading products... admin.js:681:17
API Response:
Object { success: true, products: (5) […] }
admin.js:687:17
Products data:
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
admin.js:702:17
Found 5 products admin.js:703:17
Adding view more link for product:
Object { id: 6, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=6" }
admin.js:1316:13
Adding view more link for product:
Object { id: 12, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 15, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 16, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 17, has_landing_page: 0, landing_url: null }
admin.js:1316:13
GET
http://localhost:8000/images/book3.svg
[HTTP/1.1 404 Not Found 27ms]

Navigation item clicked:
<li class="" data-section="dashboard">
admin.js:419:21
Switching to section: dashboard admin.js:433:21
Loading dashboard... admin.js:456:33
Dashboard data loaded:
Object { products: {…}, orders: {…}, sales: {…}, landing_pages: {…}, books: {…}, charts: {…}, recent_orders: [] }
admin.js:617:17
✅ Dashboard statistics updated successfully admin.js:658:17
Navigation item clicked:
<li data-section="orders">
admin.js:419:21
Switching to section: orders admin.js:433:21
Loading orders... admin.js:460:33
Navigation item clicked:
<li class="" data-section="books">
admin.js:419:21
Switching to section: books admin.js:433:21
Loading books... admin.js:464:33
📦 Calling loadProducts function... admin.js:466:21
📦 Loading products... admin.js:681:17
API Response:
Object { success: true, products: (5) […] }
admin.js:687:17
Products data:
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
admin.js:702:17
Found 5 products admin.js:703:17
Adding view more link for product:
Object { id: 6, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=6" }
admin.js:1316:13
Adding view more link for product:
Object { id: 12, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 15, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 16, has_landing_page: 0, landing_url: null }
admin.js:1316:13
Adding view more link for product:
Object { id: 17, has_landing_page: 0, landing_url: null }
admin.js:1316:13
GET
http://localhost:8000/images/book3.svg
[HTTP/1.1 404 Not Found 76ms]

🖊️ Starting edit for product ID: 17 admin.js:1412:13
📡 Response status: 200 admin.js:1419:17
📄 Raw response: {"id":17,"type":"book","titre":"\u0641\u0646 \u0627\u0644\u0644\u0627\u0645\u0628\u0627\u0644\u0627\u0629","description":"<p>fgvdfgf<\/p>","prix":"1800.00","stock":23,"image_url":null,"actif":1,"auteu... admin.js:1426:17
✅ Product data loaded:
Object { id: 17, type: "book", titre: "فن اللامبالاة", description: "<p>fgvdfgf</p>", prix: "1800.00", stock: 23, image_url: null, actif: 1, auteur: null, materiel: null, … }
admin.js:1444:17
📝 Basic fields populated admin.js:1462:17
✅ Form populated successfully admin.js:1493:17
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
La ressource média http://localhost:8000/assets/notification.mp3 n’a pu être décodée. index.html
Notification sound not available: The media resource indicated by the src attribute or assigned media provider object was not suitable. admin.js:110:33
La ressource média http://localhost:8000/assets/notification.mp3 n’a pu être décodée. index.html
Notification sound not available: The media resource indicated by the src attribute or assigned media provider object was not suitable. admin.js:110:33
La ressource multimédia http://localhost:8000/assets/notification.mp3 n’a pas pu être décodée, erreur : Error Code: NS_ERROR_DOM_MEDIA_METADATA_ERR (0x806e0006) index.html
TinyMCE editor found and initialized admin.js:1047:25
Description content: <empty string> admin.js:1049:25
Empty description content tinymce-config.js:18:25
MouseEvent.mozInputSource est obsolète. Veuillez utiliser plutôt PointerEvent.pointerType. tinymce.min.js:4:51163
Content changed in editor: productDescription admin.js:962:29
Current content: <p>ddd</p> admin.js:963:29
Un contrôle de formulaire invalide ne peut recevoir le focus. admin
Content changed in editor: productDescription admin.js:962:29
Current content: <p>hffgjhjnghjnghddd</p> admin.js:963:29
Un contrôle de formulaire invalide ne peut recevoir le focus. admin





Navigation item clicked:
<li class="" data-section="landingPages">
admin.js:419:21
Switching to section: landingPages admin.js:433:21
Loading landing pages... admin.js:473:33
Landing Pages Manager not found tinymce-config.js:18:25
Navigation item clicked:
<li class="" data-section="settings">
admin.js:419:21
Switching to section: settings admin.js:433:21
Loading settings... admin.js:491:33
Loading settings... admin.js:217:17
Starting TinyMCE initialization... admin.js:910:13
Added tinymce class to: productDescription admin.js:929:17
TinyMCE initialization - Found textareas: 1 admin.js:931:13
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
Settings response text: <br />
<b>Fatal error</b>:  Uncaught Error: Using $this when not in object context in K:\Projets_Sites_Web\Mossaab-LandingPage\php\admin.php:286
Stack trace:
#0 {main}
  thrown in <b>K:\Projets_Sites_Web\Mossaab-LandingPage\php\admin.php</b> on line <b>286</b><br />
admin.js:225:17
JSON parse error: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    loadSettings http://localhost:8000/admin/js/admin.js:234
    initNavigation http://localhost:8000/admin/js/admin.js:493
    initNavigation http://localhost:8000/admin/js/admin.js:417
    initNavigation http://localhost:8000/admin/js/admin.js:414
    <anonymous> http://localhost:8000/admin/js/admin.js:1764
    EventListener.handleEvent* http://localhost:8000/admin/js/admin.js:1753
<anonymous code>:1:145535
Response was: <br />
<b>Fatal error</b>:  Uncaught Error: Using $this when not in object context in K:\Projets_Sites_Web\Mossaab-LandingPage\php\admin.php:286
Stack trace:
#0 {main}
  thrown in <b>K:\Projets_Sites_ <anonymous code>:1:145535
Erreur lors du chargement des paramètres: Error: Invalid JSON response from settings API
    loadSettings http://localhost:8000/admin/js/admin.js:238
    initNavigation http://localhost:8000/admin/js/admin.js:493
    initNavigation http://localhost:8000/admin/js/admin.js:417
    initNavigation http://localhost:8000/admin/js/admin.js:414
    <anonymous> http://localhost:8000/admin/js/admin.js:1764
    EventListener.handleEvent* http://localhost:8000/admin/js/admin.js:1753
<anonymous code>:1:145535
TinyMCE editor initialized: storeAddress admin.js:957:29
