<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config.php';

header('Content-Type: application/json');
header('X-Debug-PHP-Errors: ' . ini_get('display_errors'));
header('X-Debug-Error-Reporting: ' . error_reporting());

function handleGet()
{
    global $conn;

    try {
        // Check if requesting a specific product
        $id = $_GET['id'] ?? null;

        if ($id) {
            // Get specific product
            $stmt = $conn->prepare(
                "SELECT p.*,
                        CASE
                            WHEN lp.id IS NOT NULL THEN true
                            ELSE false
                        END as has_landing_page,
                        lp.lien_url as landing_url
                 FROM produits p
                 LEFT JOIN landing_pages lp ON p.id = lp.produit_id
                 WHERE p.id = ?"
            );
            $stmt->execute([$id]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$product) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Product not found']);
                return;
            }

            echo json_encode($product);
            return;
        }

        // Get all products
        $stmt = $conn->prepare(
            "SELECT p.*,
                    CASE
                        WHEN lp.id IS NOT NULL THEN true
                        ELSE false
                    END as has_landing_page,
                    lp.lien_url as landing_url
             FROM produits p
             LEFT JOIN landing_pages lp ON p.id = lp.produit_id"
        );
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($products)) {
            echo json_encode(['success' => true, 'products' => []]);
            return;
        }

        // Add full URLs
        foreach ($products as &$product) {
            $product['url'] = '/Mossaab-LandingPage/product.php?id=' . $product['id'];
            if ($product['has_landing_page']) {
                $product['landing_url'] = '/Mossaab-LandingPage/landing-page.php?id=' . $product['id'];
            }
        }

        echo json_encode(['success' => true, 'products' => $products]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function handleToggleActive()
{
    global $conn;

    try {
        // Enhanced error logging
        error_log("Toggle active called with POST data: " . print_r($_POST, true));

        $id = $_POST['productId'] ?? null;
        $active = isset($_POST['active']) ? (bool)$_POST['active'] : null;

        if (!$id || $active === null) {
            error_log("Toggle active validation failed - ID: $id, Active: " . var_export($active, true));
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Product ID and active status are required']);
            return;
        }

        // Convert string values to proper boolean
        if (is_string($_POST['active'])) {
            $active = $_POST['active'] === '1' || $_POST['active'] === 'true';
        }

        error_log("Updating product $id to active status: " . ($active ? 'true' : 'false'));

        $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
        $result = $stmt->execute([$active ? 1 : 0, $id]);

        if ($result && $stmt->rowCount() > 0) {
            error_log("Product $id status updated successfully");
            echo json_encode(['success' => true, 'message' => 'Product status updated successfully']);
        } else {
            error_log("Product $id not found or no changes made");
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Product not found or no changes made']);
        }
    } catch (PDOException $e) {
        error_log("Database error in toggle active: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    } catch (Exception $e) {
        error_log("General error in toggle active: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
    }
}

function handlePost()
{
    error_log("========= DEBUG PRODUCT SUBMISSION ==========");
    error_log("Raw POST data: " . file_get_contents('php://input'));
    error_log("Content-Type header: " . $_SERVER['CONTENT_TYPE']);
    error_log("Description content: " . (isset($_POST['productDescription']) ? $_POST['productDescription'] : 'not set'));
    error_log("Description length: " . (isset($_POST['productDescription']) ? strlen($_POST['productDescription']) : 0));
    error_log("========= END DEBUG PRODUCT SUBMISSION ==========");

    error_log("Received POST request with data: " . print_r($_POST, true));
    error_log("Received FILES: " . print_r($_FILES, true));
    global $conn;

    try {
        $id = $_POST['productId'] ?? null;
        $title = $_POST['productTitle'];
        $description = $_POST['productDescription'];
        $price = $_POST['productPrice'];
        $stock = $_POST['productStock'];
        $type = $_POST['productType'];

        // Handle specific fields based on product type
        $specificFields = [];
        switch ($type) {
            case 'book':
                $specificFields['auteur'] = $_POST['productAuthor'] ?? null;
                break;
            case 'bag':
                $specificFields['materiel'] = $_POST['bagMaterial'] ?? null;
                $specificFields['capacite'] = $_POST['bagCapacity'] ?? null;
                break;
            case 'laptop':
                $specificFields['processeur'] = $_POST['laptopProcessor'] ?? null;
                $specificFields['ram'] = $_POST['laptopRam'] ?? null;
                $specificFields['stockage'] = $_POST['laptopStorage'] ?? null;
                break;
        }

        $conn->beginTransaction();

        // Handle image upload
        $imageUrl = null;
        if (!empty($_FILES['productImage']['tmp_name'])) {
            $uploadDir = '../../uploads/products/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileName = uniqid() . '_' . $_FILES['productImage']['name'];
            $filePath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['productImage']['tmp_name'], $filePath)) {
                $imageUrl = '/uploads/products/' . $fileName;
            }
        }

        if ($id) {
            // Update existing product
            $sql = "UPDATE produits SET
                    titre = ?,
                    description = ?,
                    prix = ?,
                    stock = ?,
                    type = ?";
            $params = [$title, $description, $price, $stock, $type];

            if ($imageUrl) {
                $sql .= ", image_url = ?";
                $params[] = $imageUrl;
            }

            foreach ($specificFields as $field => $value) {
                if ($value !== null) {
                    $sql .= ", $field = ?";
                    $params[] = $value;
                }
            }

            $sql .= " WHERE id = ?";
            $params[] = $id;

            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
        } else {
            // Insert new product
            $fields = ['titre', 'description', 'prix', 'stock', 'type'];
            $values = [$title, $description, $price, $stock, $type];
            $placeholders = ['?', '?', '?', '?', '?'];

            if ($imageUrl) {
                $fields[] = 'image_url';
                $values[] = $imageUrl;
                $placeholders[] = '?';
            }

            foreach ($specificFields as $field => $value) {
                if ($value !== null) {
                    $fields[] = $field;
                    $values[] = $value;
                    $placeholders[] = '?';
                }
            }

            $sql = "INSERT INTO produits (" . implode(', ', $fields) . ")
                   VALUES (" . implode(', ', $placeholders) . ")";

            error_log("SQL Query: " . $sql);
            error_log("Values: " . print_r($values, true));

            $stmt = $conn->prepare($sql);
            $stmt->execute($values);
            $id = $conn->lastInsertId();

            error_log("New product ID: " . $id);

            // Verify the insertion
            $verifyStmt = $conn->prepare("SELECT * FROM produits WHERE id = ?");
            $verifyStmt->execute([$id]);
            $newProduct = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            error_log("Newly inserted product: " . print_r($newProduct, true));
        }

        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Product saved successfully', 'id' => $id]);
    } catch (PDOException $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    } catch (Exception $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error']);
    }
}

function handleDelete()
{
    global $conn;

    try {
        // Check if this is a bulk delete request
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if ($data && isset($data['action']) && $data['action'] === 'bulk_delete' && isset($data['ids'])) {
            // Handle bulk deletion
            $ids = $data['ids'];

            if (empty($ids) || !is_array($ids)) {
                throw new Exception('No valid product IDs provided for bulk deletion');
            }

            // Validate all IDs are numeric
            foreach ($ids as $id) {
                if (!is_numeric($id)) {
                    throw new Exception('Invalid product ID provided');
                }
            }

            $conn->beginTransaction();

            $deletedCount = 0;
            $imagesToDelete = [];

            // Get image paths before deleting
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $stmt = $conn->prepare("SELECT id, image_url FROM produits WHERE id IN ($placeholders)");
            $stmt->execute($ids);
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($products as $product) {
                if ($product['image_url']) {
                    $imagesToDelete[] = $product['image_url'];
                }
            }

            // Delete products (cascade will handle related records)
            $stmt = $conn->prepare("DELETE FROM produits WHERE id IN ($placeholders)");
            $stmt->execute($ids);
            $deletedCount = $stmt->rowCount();

            $conn->commit();

            // Delete physical image files
            foreach ($imagesToDelete as $imageUrl) {
                $filePath = '../../' . ltrim($imageUrl, '/');
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            echo json_encode([
                'success' => true,
                'message' => "Successfully deleted $deletedCount products",
                'deleted_count' => $deletedCount
            ]);
        } else {
            // Handle single product deletion (existing functionality)
            $id = $_GET['id'] ?? null;

            if (!$id) {
                throw new Exception('Product ID is required');
            }

            // Get image path before deleting
            $stmt = $conn->prepare("SELECT image_url FROM produits WHERE id = ?");
            $stmt->execute([$id]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            // Delete product (cascade will handle landing pages)
            $stmt = $conn->prepare("DELETE FROM produits WHERE id = ?");
            $stmt->execute([$id]);

            // Delete physical image file if exists
            if ($product && $product['image_url']) {
                $filePath = '../../' . ltrim($product['image_url'], '/');
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            echo json_encode(['success' => true, 'message' => 'Product deleted successfully']);
        }
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Database error in handleDelete: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Error in handleDelete: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// Route requests
// Get request type
$action = $_GET['action'] ?? '';

// Log request details
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Request Action: " . $action);
error_log("Request Headers: " . print_r(getallheaders(), true));

// Route requests
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
    case 'PUT':
        if ($action === 'toggle-active') {
            handleToggleActive();
        } else {
            handlePost();
        }
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
