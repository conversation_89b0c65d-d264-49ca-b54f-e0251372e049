// Landing Pages Manager - Clean Implementation
// Use the existing notificationManager from admin.js

// Utility function for safe API calls with proper error handling
async function safeApiCall(url, options = {}) {
    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();

        // Check if response is empty
        if (!text.trim()) {
            console.warn('Empty response from API:', url);
            return [];
        }

        // Try to parse JSON
        try {
            return JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error for response:', text);
            throw new Error('Invalid JSON response from server');
        }
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Landing Pages Manager - Single Instance
const landingPagesManager = {
    initialized: false,
    modal: null,
    form: null,
    productSelect: null,
    imagePreview: null,
    addButton: null,
    selectedTemplate: null,
    currentStep: 1,
    templates: [],

    init() {
        // Prevent multiple initializations
        if (this.initialized) {
            console.log('Landing Pages Manager already initialized');
            return;
        }

        console.log('🚀 Initializing Landing Pages Manager...');

        // Check if elements exist before proceeding
        this.modal = document.getElementById('landingPageModal');
        this.form = document.getElementById('landingPageForm');
        this.productSelect = document.getElementById('productSelect');
        this.imagePreview = document.getElementById('imagePreview');
        this.addButton = document.getElementById('addLandingPageBtn');

        console.log('🔍 Checking for required DOM elements:', {
            modal: !!this.modal,
            form: !!this.form,
            productSelect: !!this.productSelect,
            imagePreview: !!this.imagePreview,
            addButton: !!this.addButton
        });

        if (!this.modal) {
            console.error('❌ Modal element #landingPageModal not found');
        }
        if (!this.form) {
            console.error('❌ Form element #landingPageForm not found');
        }
        if (!this.productSelect) {
            console.error('❌ Select element #productSelect not found');
        }
        if (!this.addButton) {
            console.error('❌ Button element #addLandingPageBtn not found');
        }

        if (!this.modal || !this.form || !this.productSelect || !this.addButton) {
            console.error('❌ Required elements not found for landing pages manager - initialization failed');
            return;
        }

        console.log('✅ All required elements found, proceeding with initialization...');

        this.bindEvents();
        this.loadTemplates(); // Load templates first
        this.loadActiveProducts(); // Load only active products for selection

        // Force show landing pages section for testing
        this.ensureSectionVisible();

        this.loadLandingPages();

        // Mark as initialized
        this.initialized = true;
        console.log('🎉 Landing Pages Manager initialized successfully');

        // Add a test to verify button works
        this.testButtonFunctionality();
    },

    // Load available templates
    async loadTemplates() {
        try {
            console.log('📋 Loading templates...');
            const response = await safeApiCall('../php/api/templates.php?action=get_templates');

            if (response.success && response.templates) {
                this.templates = response.templates;
                this.displayTemplates();
                console.log('✅ Templates loaded successfully:', this.templates.length);
            } else {
                console.error('❌ Failed to load templates:', response);
            }
        } catch (error) {
            console.error('❌ Error loading templates:', error);
        }
    },

    // Display templates in the selection grid
    displayTemplates() {
        const templateGrid = document.getElementById('templateGrid');
        if (!templateGrid) return;

        templateGrid.innerHTML = this.templates.map(template => `
            <div class="template-card" data-template-id="${template.id}" onclick="landingPagesManager.selectTemplate('${template.id}')">
                <i class="${template.icon} template-icon"></i>
                <div class="template-name">${template.name}</div>
                <div class="template-description">${template.description}</div>
            </div>
        `).join('');
    },

    // Select a template
    selectTemplate(templateId) {
        // Remove previous selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked template
        const selectedCard = document.querySelector(`[data-template-id="${templateId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
            this.selectedTemplate = this.templates.find(t => t.id === templateId);

            // Enable next button
            const nextBtn = document.getElementById('nextStepBtn');
            if (nextBtn) {
                nextBtn.disabled = false;
            }

            console.log('✅ Template selected:', this.selectedTemplate);
        }
    },

    // Navigate to next step
    nextStep() {
        if (!this.selectedTemplate) {
            notificationManager.showError('يرجى اختيار قالب أولاً');
            return;
        }

        // Hide template selection step
        const templateStep = document.getElementById('templateSelectionStep');
        const contentStep = document.getElementById('contentStep');

        if (templateStep) templateStep.style.display = 'none';
        if (contentStep) contentStep.style.display = 'block';

        this.currentStep = 2;

        // Pre-fill form with template data
        this.applyTemplate();

        console.log('📝 Moved to content step');
    },

    // Navigate to previous step
    previousStep() {
        const templateStep = document.getElementById('templateSelectionStep');
        const contentStep = document.getElementById('contentStep');

        if (templateStep) templateStep.style.display = 'block';
        if (contentStep) contentStep.style.display = 'none';

        this.currentStep = 1;

        console.log('🔙 Moved back to template step');
    },

    // Apply selected template to form
    async applyTemplate() {
        if (!this.selectedTemplate || !this.selectedTemplate.content) {
            console.warn('No template or template content available');
            return;
        }

        const template = this.selectedTemplate.content;
        console.log('Applying template:', template);

        try {
            // Initialize TinyMCE first
            await this.initModalTinyMCE();

            // Set hidden template field
            const templateField = document.getElementById('selectedTemplate');
            if (templateField) {
                templateField.value = this.selectedTemplate.id;
            }

            // Set layout positions
            const imagePosition = document.getElementById('imagePosition');
            const textPosition = document.getElementById('textPosition');

            if (imagePosition && template.image_position) {
                imagePosition.value = template.image_position;
            }

            if (textPosition && template.text_position) {
                textPosition.value = template.text_position;
            }

            // Set title template (will be updated when product is selected)
            const titleField = document.getElementById('landingPageTitle');
            if (titleField && template.titre) {
                titleField.value = template.titre;
            }

            // Set content in TinyMCE editors with improved error handling
            const setEditorContent = async (editorId, content) => {
                const maxAttempts = 5;
                const interval = 500;
                let attempts = 0;

                while (attempts < maxAttempts) {
                    const editor = tinymce.get(editorId);
                    if (editor && editor.initialized) {
                        try {
                            editor.setContent(content || '');
                            console.log(`Content set successfully for ${editorId}`);
                            return;
                        } catch (error) {
                            console.error(`Error setting content for ${editorId}:`, error);
                            if (attempts === maxAttempts - 1) throw error;
                        }
                    }
                    await new Promise(resolve => setTimeout(resolve, interval));
                    attempts++;
                }
                throw new Error(`Failed to set content for ${editorId} after ${maxAttempts} attempts`);
            };

            // Set content for both editors with error handling
            await Promise.all([
                template.contenu_droit ? setEditorContent('rightContent', template.contenu_droit) : Promise.resolve(),
                template.contenu_gauche ? setEditorContent('leftContent', template.contenu_gauche) : Promise.resolve()
            ]);

            console.log('✅ Template applied to form successfully');
        } catch (error) {
            console.error('Error applying template:', error);
            notificationManager.showError('حدث خطأ أثناء تطبيق القالب');
        }
    },

    // Reset modal to step 1 (template selection)
    resetModalToStep1() {
        this.currentStep = 1;
        this.selectedTemplate = null;

        // Show template step, hide content step
        const templateStep = document.getElementById('templateSelectionStep');
        const contentStep = document.getElementById('contentStep');

        if (templateStep) templateStep.style.display = 'block';
        if (contentStep) contentStep.style.display = 'none';

        // Reset template selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Disable next button
        const nextBtn = document.getElementById('nextStepBtn');
        if (nextBtn) {
            nextBtn.disabled = true;
        }

        console.log('🔄 Modal reset to step 1');
    },

    bindEvents() {
        console.log('🔗 Binding events...');

        // Add button click handler
        if (this.addButton) {
            console.log('✅ Binding click event to add button:', this.addButton);
            this.addButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🖱️ Add landing page button clicked!');
                console.log('Button element:', e.target);
                console.log('Opening modal...');
                this.openModal();
            });
            console.log('✅ Add button event bound successfully');
        } else {
            console.error('❌ Add button not found - cannot bind click event');
        }

        // Form submit handler
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // Image upload handler
        const imageInput = document.getElementById('landingPageImages');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        // Close button handler
        const closeButton = this.modal ? this.modal.querySelector('.cancel-button') : null;
        if (closeButton) {
            closeButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal();
            });
        }

        // Product selection change handler
        if (this.productSelect) {
            this.productSelect.addEventListener('change', (e) => {
                this.handleProductSelection(e.target.value);
            });
        }

        // Close modal when clicking outside
        if (this.modal) {
            window.addEventListener('click', (event) => {
                if (event.target === this.modal) {
                    this.closeModal();
                }
            });
        }
    },

    async loadActiveProducts() {
        try {
            console.log('📦 Loading active products for landing page selection...');
            const response = await safeApiCall('../php/api/products.php');

            // Handle both array and object responses
            let products = [];
            if (Array.isArray(response)) {
                products = response;
            } else if (response.success && Array.isArray(response.products)) {
                products = response.products;
            } else if (response.products) {
                products = response.products;
            }

            this.productSelect.innerHTML = '<option value="">اختر منتجاً</option>';

            // Filter only active products
            const activeProducts = products.filter(product => product.actif == 1 || product.actif === true);

            console.log(`Found ${activeProducts.length} active products out of ${products.length} total products`);

            if (activeProducts.length === 0) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'لا توجد منتجات مفعلة';
                option.disabled = true;
                this.productSelect.appendChild(option);
                return;
            }

            activeProducts.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.titre || product.title || `Product ${product.id}`} (${this.getProductTypeText(product.type)})`;
                option.setAttribute('data-product-type', product.type);
                this.productSelect.appendChild(option);
            });

            console.log('Active products loaded successfully');
        } catch (error) {
            console.error('Error loading active products:', error);
            this.productSelect.innerHTML = '<option value="">خطأ في تحميل المنتجات</option>';
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات المفعلة');
        }
    },

    // Helper function to get product type text in Arabic
    getProductTypeText(type) {
        const types = {
            'book': 'كتاب',
            'laptop': 'حاسوب محمول',
            'bag': 'حقيبة'
        };
        return types[type] || type;
    },

    // Function to refresh product selection (called when products are activated/deactivated)
    refreshProductSelect() {
        console.log('Refreshing product selection...');
        this.loadActiveProducts();
    },

    // Ensure landing pages section is visible
    ensureSectionVisible() {
        console.log('👁️ Ensuring landing pages section is visible...');
        const section = document.getElementById('landingPages');
        if (section) {
            const currentDisplay = window.getComputedStyle(section).display;
            const hasActiveClass = section.classList.contains('active');
            console.log('👁️ Current section display:', currentDisplay);
            console.log('👁️ Has active class:', hasActiveClass);

            if (currentDisplay === 'none' || !hasActiveClass) {
                console.log('👁️ Making section visible by adding active class...');

                // Remove active class from all sections first
                document.querySelectorAll('.content-section').forEach(s => {
                    s.classList.remove('active');
                });

                // Add active class to landing pages section
                section.classList.add('active');

                // Also update navigation
                document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                    navItem.classList.remove('active');
                });
                const navItem = document.querySelector('.admin-nav ul li[data-section="landingPages"]');
                if (navItem) {
                    navItem.classList.add('active');
                    console.log('👁️ Navigation item activated');
                }
            }

            // Also check if container exists
            const container = document.getElementById('landingPagesContainer');
            console.log('👁️ Container exists:', !!container);
            if (container) {
                console.log('👁️ Container display:', window.getComputedStyle(container).display);
            }
        } else {
            console.error('❌ Landing pages section not found!');
        }
    },

    // Test button functionality
    testButtonFunctionality() {
        console.log('🧪 Testing button functionality...');

        if (this.addButton) {
            console.log('✅ Add button found:', this.addButton);
            console.log('Button text:', this.addButton.textContent);
            console.log('Button ID:', this.addButton.id);
            console.log('Button classes:', this.addButton.className);

            // Test if button is clickable
            const rect = this.addButton.getBoundingClientRect();
            console.log('Button position:', rect);
            console.log('Button visible:', rect.width > 0 && rect.height > 0);

            // Check if button's parent section is visible
            const section = this.addButton.closest('.content-section');
            if (section) {
                const sectionRect = section.getBoundingClientRect();
                console.log('Section position:', sectionRect);
                console.log('Section visible:', sectionRect.width > 0 && sectionRect.height > 0);
                console.log('Section display:', window.getComputedStyle(section).display);
            }

            // Add a temporary test click handler
            const testHandler = () => {
                console.log('🎉 TEST: Button click detected!');
            };

            this.addButton.addEventListener('click', testHandler, { once: true });
            console.log('✅ Test click handler added');
        } else {
            console.error('❌ Add button not found during test');
        }
    },

    // Handle product selection change
    handleProductSelection(productId) {
        const statusDiv = document.getElementById('productSelectionStatus');
        const titleInput = document.getElementById('landingPageTitle');

        if (!statusDiv) return;

        if (!productId) {
            statusDiv.style.display = 'none';
            if (titleInput) titleInput.value = '';
            return;
        }

        // Find the selected product option
        const selectedOption = this.productSelect.querySelector(`option[value="${productId}"]`);
        if (selectedOption) {
            const productName = selectedOption.textContent;
            const productType = selectedOption.getAttribute('data-product-type');

            statusDiv.className = 'product-selection-status active';
            statusDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                تم اختيار: ${productName}
            `;

            // Auto-fill the landing page title based on template
            if (titleInput) {
                const cleanProductName = productName.split(' (')[0]; // Remove type info

                if (this.selectedTemplate && this.selectedTemplate.content && this.selectedTemplate.content.titre) {
                    // Use template title and replace placeholder
                    titleInput.value = this.selectedTemplate.content.titre.replace('{product_title}', cleanProductName);
                } else if (!titleInput.value) {
                    // Fallback to default title
                    titleInput.value = `صفحة هبوط - ${cleanProductName}`;
                }
            }
        }
    },

    async loadLandingPages() {
        console.log('🔄 Loading landing pages...');
        try {
            // Add cache-busting parameter to ensure fresh data
            const cacheBuster = Date.now();
            const apiUrl = `../php/api/landing-pages.php?_t=${cacheBuster}`;
            console.log('📡 Making API call to:', apiUrl);

            const response = await safeApiCall(apiUrl);
            console.log('📡 Raw API Response:', response);
            console.log('📡 Response type:', typeof response);
            console.log('📡 Response keys:', Object.keys(response || {}));

            const pages = response.data || response; // Handle both formats
            console.log('📋 Landing pages data:', pages);
            console.log('📋 Pages type:', typeof pages);
            console.log('📋 Is array:', Array.isArray(pages));
            console.log('📋 Pages length:', pages ? pages.length : 'undefined');

            this.displayLandingPages(pages);

            // Force a visual refresh of the container
            const container = document.getElementById('landingPagesContainer');
            if (container) {
                container.style.opacity = '0.5';
                setTimeout(() => {
                    container.style.opacity = '1';
                }, 100);
            }
        } catch (error) {
            console.error('❌ Error loading landing pages:', error);
            console.error('❌ Error stack:', error.stack);
            notificationManager.showError('حدث خطأ أثناء تحميل صفحات الهبوط: ' + error.message);
        }
    },

    displayLandingPages(pages) {
        console.log('🎨 Displaying landing pages:', pages);
        console.log('🎨 Pages parameter type:', typeof pages);
        console.log('🎨 Pages parameter value:', pages);

        const container = document.getElementById('landingPagesContainer');
        console.log('🎨 Container found:', !!container);
        console.log('🎨 Container element:', container);

        if (!container) {
            console.error('❌ Landing pages container not found!');
            return;
        }

        if (!Array.isArray(pages) || pages.length === 0) {
            console.log('📭 No landing pages to display');
            console.log('📭 Pages is array:', Array.isArray(pages));
            console.log('📭 Pages length:', pages ? pages.length : 'undefined');
            container.innerHTML = '<p class="no-data" style="text-align: center; padding: 40px; color: #666;">لا توجد صفحات هبوط</p>';
            return;
        }

        console.log(`📄 Displaying ${pages.length} landing pages`);

        container.innerHTML = pages.map(page => `
            <div class="landing-page-card" style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
                    <div>
                        <h3 style="margin: 0 0 10px 0; color: #333;">${page.titre || 'صفحة هبوط'}</h3>
                        <p style="margin: 0; color: #666;">📦 المنتج: ${page.product_title || 'غير محدد'}</p>
                        <p style="margin: 5px 0 0 0; color: #888; font-size: 0.9em;">🔗 ${page.lien_url || ''}</p>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        ${page.images && page.images.length > 0 ? `<span style="background: #e8f4fd; color: #667eea; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">📸 ${page.images.length} صور</span>` : ''}
                    </div>
                </div>
                <div class="page-actions" style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <a href="${page.lien_url || '#'}" target="_blank" class="view-btn" style="background: #28a745; color: white; padding: 8px 15px; border-radius: 5px; text-decoration: none; font-size: 0.9em;">
                        <i class="fas fa-eye"></i> عرض
                    </a>
                    <button onclick="landingPagesManager.clonePage(${page.id})" class="clone-btn" style="background: #17a2b8; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                    <button onclick="landingPagesManager.editPage(${page.id})" class="edit-btn" style="background: #ffc107; color: #333; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button onclick="landingPagesManager.deletePage(${page.id})" class="delete-btn" style="background: #dc3545; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button onclick="copyPageUrl('${page.lien_url || ''}', this)" class="copy-btn" style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;">
                        <i class="fas fa-link"></i> نسخ الرابط
                    </button>
                </div>
            </div>
        `).join('');
    },

    async openModal() {
        console.log('🚀 Opening modal...');

        if (!this.modal) {
            console.error('❌ Modal element not found');
            return;
        }

        try {
            // Ensure all form controls are enabled and accessible
            const formControls = this.modal.querySelectorAll('input, select, textarea, button');
            formControls.forEach(control => {
                control.disabled = false;
                if (control.hasAttribute('required')) {
                    control.setAttribute('aria-required', 'true');
                }
            });

            // Ensure the landing pages section is visible first
            const landingPagesSection = document.getElementById('landingPages');
            if (landingPagesSection) {
                const sectionDisplay = window.getComputedStyle(landingPagesSection).display;
                console.log('Landing pages section display:', sectionDisplay);

                if (sectionDisplay === 'none') {
                    console.log('⚠️ Section not visible, making it visible first...');
                    landingPagesSection.style.display = 'block';
                }
            }

            // Force modal display with important styles
            this.modal.style.setProperty('display', 'block', 'important');
            this.modal.style.setProperty('opacity', '1', 'important');
            this.modal.style.setProperty('visibility', 'visible', 'important');
            this.modal.style.setProperty('z-index', '9999', 'important');
            this.modal.style.setProperty('position', 'fixed', 'important');
            this.modal.style.setProperty('top', '0', 'important');
            this.modal.style.setProperty('left', '0', 'important');
            this.modal.style.setProperty('width', '100%', 'important');
            this.modal.style.setProperty('height', '100%', 'important');

            // Add a class for CSS targeting
            this.modal.classList.add('modal-open');

            // Reset to step 1 (template selection)
            this.resetModalToStep1();

            // Reset form
            if (this.form) {
                this.form.reset();
            }

            // Clear image preview
            if (this.imagePreview) {
                this.imagePreview.innerHTML = '';
            }

            // Refresh active products list to ensure it's up to date
            await this.refreshProductSelect();

            // Initialize TinyMCE editors
            await this.initModalTinyMCE();

            console.log('✅ Modal opened successfully');

            // Debug: log modal styles
            console.log('Modal styles:', {
                display: this.modal.style.display,
                opacity: this.modal.style.opacity,
                visibility: this.modal.style.visibility,
                zIndex: this.modal.style.zIndex,
                position: this.modal.style.position
            });
        } catch (error) {
            console.error('Error opening modal:', error);
            notificationManager.showError('حدث خطأ أثناء فتح النافذة');
        }
    },

    async closeModal() {
        console.log('🚪 Closing modal...');

        if (!this.modal) {
            console.error('❌ Modal not found for closing');
            return;
        }

        try {
            // Reset all form validation states
            const formControls = this.modal.querySelectorAll('input, select, textarea');
            formControls.forEach(control => {
                control.classList.remove('invalid', 'error', 'is-invalid', 'is-valid');
                control.removeAttribute('aria-invalid');
                if (control.nextElementSibling && control.nextElementSibling.classList.contains('error-message')) {
                    control.nextElementSibling.remove();
                }
            });

            // Reset TinyMCE editors before cleanup
            if (window.tinymce) {
                const editors = tinymce.editors;
                for (const editor of editors) {
                    if (editor) {
                        try {
                            // Clear content and undo history
                            editor.setContent('');
                            editor.undoManager.clear();
                            // Set to readonly mode before removal
                            if (editor.mode && editor.mode.set) {
                                editor.mode.set('readonly');
                            }
                        } catch (editorError) {
                            console.warn('Error resetting editor:', editor.id, editorError);
                        }
                    }
                }
            }

            // Hide modal with important styles
            this.modal.style.setProperty('display', 'none', 'important');
            this.modal.style.setProperty('opacity', '0', 'important');
            this.modal.style.setProperty('visibility', 'hidden', 'important');

            // Clean up TinyMCE instances
            await this.cleanupTinyMCE();

            // Remove modal-open class
            this.modal.classList.remove('modal-open');

            // Reset form and remove edit ID
            if (this.form) {
                this.form.reset();
                this.form.removeAttribute('data-edit-id');
                // Remove any custom validation classes
                this.form.querySelectorAll('.form-control').forEach(control => {
                    control.classList.remove('is-invalid', 'is-valid');
                });
            }

            // Clear image preview
            if (this.imagePreview) {
                this.imagePreview.innerHTML = '';
            }

            // Reset modal title
            const modalTitle = document.querySelector('#landingPageModal .modal-header h3');
            if (modalTitle) {
                modalTitle.textContent = 'إضافة صفحة هبوط جديدة';
            }

            // Reset submit button text
            const submitBtn = this.form ? this.form.querySelector('button[type="submit"]') : null;
            if (submitBtn) {
                submitBtn.textContent = 'حفظ';
                submitBtn.disabled = false;
            }

            // Remove any hidden deleted images input
            const deletedImagesInput = this.form ? this.form.querySelector('input[name="deleted_images"]') : null;
            if (deletedImagesInput) {
                deletedImagesInput.remove();
            }

            console.log('✅ Modal closed successfully');
        } catch (error) {
            console.error('Error closing modal:', error);
            notificationManager.showError('حدث خطأ أثناء إغلاق النافذة');
        }
    },

    initModalTinyMCE() {
        console.log('Initializing TinyMCE for landing pages modal');

        return new Promise((resolve, reject) => {
            // Clean up existing instances first
            this.cleanupTinyMCE();

            let initAttempts = 0;
            const maxAttempts = 5;
            const initInterval = 200;

            const tryInit = () => {
                console.log(`TinyMCE initialization attempt ${initAttempts + 1}/${maxAttempts}`);

                const rightContent = document.getElementById('rightContent');
                const leftContent = document.getElementById('leftContent');

                if (!rightContent || !leftContent) {
                    if (initAttempts < maxAttempts) {
                        initAttempts++;
                        console.warn('TinyMCE target elements not found, retrying...');
                        setTimeout(tryInit, initInterval);
                        return;
                    }
                    const error = new Error('TinyMCE target elements not found after maximum attempts');
                    console.error(error);
                    reject(error);
                    return;
                }

                if (typeof tinymce === 'undefined') {
                    if (initAttempts < maxAttempts) {
                        initAttempts++;
                        console.warn('TinyMCE not loaded, retrying...');
                        setTimeout(tryInit, initInterval);
                        return;
                    }
                    const error = new Error('TinyMCE not loaded after maximum attempts');
                    console.error(error);
                    reject(error);
                    return;
                }

                tinymce.init({
                    selector: '#rightContent, #leftContent',
                    directionality: 'rtl',
                    language: 'ar',
                    height: 300,
                    readonly: false,
                    plugins: 'lists link image media table code wordcount autoresize',
                    toolbar: 'undo redo | formatselect | bold italic | alignright aligncenter alignleft | bullist numlist | link image media | table | code',
                    content_css: [
                        'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap'
                    ],
                    content_style: 'body { font-family: "Noto Sans Arabic", sans-serif; direction: rtl; text-align: right; }',
                    promotion: false,
                    branding: false,
                    menubar: false,
                    autoresize_bottom_margin: 50,
                    autoresize_min_height: 300,
                    paste_data_images: true,
                    image_advtab: true,
                    convert_urls: false,
                    relative_urls: false,
                    remove_script_host: false,
                    setup: function(editor) {
                        editor.on('init', function() {
                            try {
                                console.log('TinyMCE initialized for:', editor.id);
                                const editorContainer = editor.getContainer();
                                if (editorContainer) {
                                    editorContainer.style.direction = 'rtl';

                                    // Add ARIA attributes
                                    const iframe = editorContainer.querySelector('iframe');
                                    if (iframe) {
                                        iframe.setAttribute('aria-required', 'true');
                                        iframe.setAttribute('aria-label', editor.id === 'rightContent' ? 'محرر المحتوى الأيمن' : 'محرر المحتوى الأيسر');
                                    }
                                }

                                // Force editable mode with error handling
                                if (editor.mode && editor.mode.set) {
                                    editor.mode.set('design');
                                }

                                const body = editor.getBody();
                                if (body) {
                                    body.removeAttribute('readonly');
                                    body.contentEditable = true;

                                    // Additional initialization checks
                                    if (!body.isContentEditable) {
                                        console.warn('Editor body not editable after initialization');
                                        body.contentEditable = true;
                                    }
                                }

                                // Verify editor state
                                if (!editor.initialized) {
                                    console.warn('Editor not fully initialized:', editor.id);
                                    editor.initialized = true;
                                }
                            } catch (error) {
                                console.error('Error during editor initialization:', error);
                                reject(error);
                            }
                        });

                    // Add validation handling
                    editor.on('change', function() {
                        const content = editor.getContent().trim();
                        const editorContainer = editor.getContainer();

                        if (content) {
                            editorContainer.classList.remove('invalid');
                            editorContainer.setAttribute('aria-invalid', 'false');
                        } else {
                            editorContainer.classList.add('invalid');
                            editorContainer.setAttribute('aria-invalid', 'true');
                        }
                    });
                },
                init_instance_callback: function(editor) {
                    console.log('TinyMCE ready for:', editor.id);
                    // Ensure editor is fully editable
                    try {
                        // setMode is deprecated in newer versions, use mode.set instead
                        if (editor.mode && editor.mode.set) {
                            editor.mode.set('design');
                        }
                        editor.getBody().removeAttribute('readonly');
                        editor.getBody().contentEditable = true;
                    } catch (error) {
                        console.warn('Could not set editor mode:', error);
                    }
                }
                }).then(() => {
                    console.log('TinyMCE initialization successful');
                    resolve();
                }).catch(error => {
                    console.error('TinyMCE initialization failed:', error);
                    reject(error);
                });
            };

            tryInit();
        }).catch(error => {
            console.error('Failed to initialize TinyMCE:', error);
            notificationManager.showError('حدث خطأ أثناء تهيئة المحرر');
            this.fallbackToTextareas();
        });
    },

    fallbackToTextareas() {
        console.log('Falling back to regular textareas for content editing');
        ['rightContent', 'leftContent'].forEach(id => {
            const textarea = document.getElementById(id);
            if (textarea) {
                textarea.style.display = 'block';
                textarea.style.width = '100%';
                textarea.style.minHeight = '300px';
                textarea.style.direction = 'rtl';
                textarea.style.textAlign = 'right';
                textarea.style.fontFamily = 'Noto Sans Arabic, sans-serif';
                textarea.style.padding = '10px';
                textarea.style.border = '1px solid #ddd';
                textarea.style.borderRadius = '4px';
                textarea.style.marginBottom = '15px';
            }
        });
    }
    },

    async cleanupTinyMCE() {
        if (typeof tinymce === 'undefined') {
            console.log('TinyMCE not found, no cleanup needed');
            return;
        }

        try {
            // Get all editor instances
            const editors = tinymce.editors;
            if (!editors || editors.length === 0) {
                console.log('No TinyMCE editors found to clean up');
                return;
            }

            // Create a copy of the editors array since we'll be modifying it
            const editorsToRemove = [...editors];

            for (const editor of editorsToRemove) {
                if (editor) {
                    try {
                        // Clear content and undo history first
                        editor.setContent('');
                        editor.undoManager.clear();

                        // Set to readonly mode before removal
                        if (editor.mode && editor.mode.set) {
                            editor.mode.set('readonly');
                        }

                        // Remove the editor instance
                        editor.remove();
                        console.log(`Editor ${editor.id} removed successfully`);
                    } catch (editorError) {
                        console.warn(`Error removing editor ${editor.id}:`, editorError);
                    }
                }
            }

            // Final cleanup of any remaining instances
            tinymce.remove();

            // Verify cleanup
            const remainingEditors = tinymce.editors;
            if (remainingEditors && remainingEditors.length > 0) {
                console.warn(`${remainingEditors.length} editor(s) still remain after cleanup`);
                // Force remove any remaining editors
                tinymce.remove();
            } else {
                console.log('All TinyMCE editors cleaned up successfully');
            }
        } catch (error) {
            console.error('Error during TinyMCE cleanup:', error);
            throw error; // Propagate error to caller
        }
    },

    handleImageUpload(event) {
        const files = event.target.files;

        // Don't clear existing previews - allow accumulation of multiple images
        // this.imagePreview.innerHTML = '';

        Array.from(files).forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const target = e.target;
                if (!target || !target.result) return;
                const preview = document.createElement('div');
                preview.className = 'preview-image';
                preview.style.cssText = `
                    background-image: url(${target.result.toString()});
                    background-size: cover;
                    background-position: center;
                    width: 100px;
                    height: 100px;
                    border-radius: 8px;
                    position: relative;
                    display: inline-block;
                    margin: 5px;
                    border: 2px solid #ddd;
                `;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.style.cssText = `
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                    font-size: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;
                removeBtn.onclick = () => {
                    preview.remove();
                    // Also remove the corresponding file from the input
                    this.updateFileInput();
                };

                preview.appendChild(removeBtn);
                this.imagePreview.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });

        console.log(`📸 Added ${files.length} images to preview. Total previews: ${this.imagePreview.children.length}`);
    },

    // Update file input to reflect removed images
    updateFileInput() {
        const imageInput = document.getElementById('landingPageImages');
        if (!imageInput) return;

        // Create a new FileList with remaining files
        const remainingPreviews = this.imagePreview.querySelectorAll('.preview-image');

        // Note: We can't perfectly sync file input with previews due to browser security
        // But we can at least clear it if no previews remain
        if (remainingPreviews.length === 0 && imageInput) {
            imageInput.value = '';
        }

        console.log(`📸 Updated file input. Remaining previews: ${remainingPreviews.length}`);
    },

    // Clear all images from preview and input
    clearAllImages() {
        // Clear the preview
        if (this.imagePreview) {
            this.imagePreview.innerHTML = '';
        }

        // Clear the file input
        const imageInput = document.getElementById('landingPageImages');
        if (imageInput) {
            imageInput.value = '';
        }

        console.log('🗑️ All images cleared');
    },

    async handleSubmit(event) {
        event.preventDefault();

        // Validate required fields before submission
        const productSelect = document.getElementById('productSelect');
        const titleInput = document.getElementById('landingPageTitle');
        const rightEditor = tinymce.get('rightContent');
        const leftEditor = tinymce.get('leftContent');

        // Check product selection
        if (!productSelect.value) {
            notificationManager.showError('يرجى اختيار منتج');
            productSelect.focus();
            return;
        }

        // Check title
        if (!titleInput.value.trim()) {
            notificationManager.showError('يرجى إدخال عنوان الصفحة');
            titleInput.focus();
            return;
        }

        // Check content
        if (!rightEditor || !rightEditor.getContent().trim()) {
            notificationManager.showError('يرجى إدخال المحتوى الأيمن');
            if (rightEditor) {
                rightEditor.focus();
            }
            return;
        }

        if (!leftEditor || !leftEditor.getContent || !leftEditor.getContent().trim()) {
            notificationManager.showError('يرجى إدخال المحتوى الأيسر');
            if (leftEditor && typeof leftEditor.focus === 'function') {
                leftEditor.focus();
            }
            return;
        }

        // Check template selection
        const templateField = document.getElementById('selectedTemplate');
        if (!templateField.value) {
            notificationManager.showError('يرجى اختيار قالب');
            return;
        }

        const formData = new FormData(this.form);
        const editId = this.form.getAttribute('data-edit-id');
        const isEdit = !!editId;

        console.log('📤 Submitting form...', isEdit ? 'Edit mode' : 'Create mode');

        // Add edit ID if in edit mode
        if (isEdit) {
            formData.append('id', editId);
            formData.append('action', 'update');
        }

        // Get TinyMCE content safely
        try {
            const rightEditor = tinymce.get('rightContent');
            const leftEditor = tinymce.get('leftContent');

            if (rightEditor) {
                formData.append('rightContent', rightEditor.getContent());
            }
            if (leftEditor) {
                formData.append('leftContent', leftEditor.getContent());
            }
        } catch (error) {
            console.warn('Error getting TinyMCE content:', error);
        }

        // Add file images to FormData
        const imageInput = document.getElementById('landingPageImages');
        if (imageInput && imageInput.files.length > 0) {
            Array.from(imageInput.files).forEach((file, index) => {
                formData.append('landingPageImages[]', file);
            });
            console.log(`📸 Added ${imageInput.files.length} files to form data`);
        }

        // Add URL images to FormData
        const urlInputs = document.querySelectorAll('input[name="imageUrls[]"]');
        urlInputs.forEach((input, index) => {
            if (input.value.trim()) {
                formData.append(`imageUrls[${index}]`, input.value.trim());
            }
        });
        console.log(`🌐 Added ${Array.from(urlInputs).filter(i => i.value.trim()).length} URL images to form data`);

        try {
            console.log('📤 Sending form data to API...');
            const method = isEdit ? 'PUT' : 'POST';
            const response = await fetch('../php/api/landing-pages.php', {
                method: method,
                body: formData
            });

            console.log('📥 Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ HTTP Error:', response.status, errorText);
                throw new Error(`HTTP ${response.status}: ${errorText.substring(0, 200)}`);
            }

            const responseText = await response.text();
            console.log('📥 Raw response:', responseText);

            let result;
            try {
                // Look for JSON in the response, even if there are PHP warnings before it
                const jsonMatch = responseText.match(/\{.*\}$/);
                if (jsonMatch) {
                    result = JSON.parse(jsonMatch[0]);
                    if (responseText !== jsonMatch[0]) {
                        console.warn('⚠️ PHP warnings detected in response:', responseText.replace(jsonMatch[0], ''));
                    }
                } else {
                    result = JSON.parse(responseText);
                }
            } catch (parseError) {
                console.error('❌ JSON Parse Error:', parseError);
                console.error('❌ Response was:', responseText);
                throw new Error('Réponse invalide du serveur (pas du JSON)');
            }

            if (result.success) {
                const successMessage = isEdit ? 'تم تحديث صفحة الهبوط بنجاح' : 'تم إنشاء صفحة الهبوط بنجاح';
                console.log('✅ Landing page operation successful');

                // First reload the landing pages, then close modal
                console.log('🔄 Reloading landing pages after successful operation...');
                await this.loadLandingPages();

                // Add a small delay to ensure UI updates
                setTimeout(() => {
                    this.closeModal();
                    notificationManager.showSuccess(successMessage);

                    // Force refresh the landing pages section if it's currently active
                    const landingPagesSection = document.getElementById('landingPages');
                    if (landingPagesSection && landingPagesSection.classList.contains('active')) {
                        console.log('🔄 Landing pages section is active, forcing refresh...');
                        setTimeout(() => {
                            this.loadLandingPages();
                        }, 500);
                    }
                }, 200);
            } else {
                const errorMessage = isEdit ? 'حدث خطأ أثناء تحديث صفحة الهبوط' : 'حدث خطأ أثناء إنشاء صفحة الهبوط';
                notificationManager.showError(result.message || errorMessage);
            }
        } catch (error) {
            console.error('❌ Error submitting form:', error);
            const errorMessage = isEdit ? 'حدث خطأ أثناء تحديث صفحة الهبوط' : 'حدث خطأ أثناء إنشاء صفحة الهبوط';
            notificationManager.showError(`${errorMessage}: ${error.message}`);
        }
    },

    async editPage(id) {
        console.log('🖊️ Starting edit for landing page ID:', id);

        // Safe notification function
        const showNotification = (message, type = 'info') => {
            try {
                if (typeof notificationManager !== 'undefined' && notificationManager[`show${type.charAt(0).toUpperCase() + type.slice(1)}`]) {
                    notificationManager[`show${type.charAt(0).toUpperCase() + type.slice(1)}`](message);
                } else {
                    console.log(`${type.toUpperCase()}: ${message}`);
                }
            } catch (notifError) {
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        };

        // Step 1: Show loading message
        showNotification('جاري تحميل بيانات الصفحة...', 'info');

        try {
            // Step 2: Get the landing page data with simple fetch
            console.log('📡 Fetching page data...');
            const response = await fetch(`../php/api/landing-pages.php?id=${id}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const text = await response.text();
            console.log('📄 Raw response:', text.substring(0, 200) + '...');

            if (!text.trim()) {
                throw new Error('الاستجابة فارغة من الخادم');
            }

            let result;
            try {
                result = JSON.parse(text);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                throw new Error('استجابة غير صحيحة من الخادم');
            }

            // Step 3: Extract page data
            let pageData = null;
            if (result.success && result.data) {
                pageData = result.data;
            } else if (result.data) {
                pageData = result.data;
            } else {
                throw new Error('لم يتم العثور على بيانات الصفحة');
            }

            console.log('✅ Page data loaded:', pageData);

            // Step 4: Open modal and populate form
            console.log('🚀 Opening modal for editing...');
            this.openEditModal(pageData);

        } catch (error) {
            console.error('❌ Error in editPage:', error);
            showNotification('حدث خطأ أثناء تحميل بيانات الصفحة: ' + error.message, 'error');
        }
    },

    openEditModal(pageData) {
        console.log('📝 Opening edit modal with data:', pageData);

        // Step 1: Open the modal first
        this.openModal();

        // Step 2: Wait a bit for modal to be ready, then populate
        setTimeout(() => {
            this.fillEditForm(pageData);
        }, 500);
    },

    fillEditForm(pageData) {
        console.log('📝 Filling edit form with:', pageData);

        try {
            // Mark form as edit mode
            if (this.form) {
                this.form.setAttribute('data-edit-id', pageData.id);
                console.log('✅ Form marked as edit mode');
            }

            // Update modal title
            const modalTitle = document.querySelector('#landingPageModal .modal-header h3');
            if (modalTitle) {
                modalTitle.textContent = 'تعديل صفحة الهبوط';
                console.log('✅ Modal title updated');
            }

            // Fill title field
            const titleInput = document.getElementById('landingPageTitle');
            if (titleInput && pageData.titre) {
                titleInput.value = pageData.titre;
                console.log('✅ Title field filled');
            }

            // Fill product selection
            const productSelect = document.getElementById('productSelect');
            if (productSelect && pageData.produit_id) {
                productSelect.value = pageData.produit_id;
                console.log('✅ Product selection filled');
            }

            // Update submit button
            const submitBtn = this.form ? this.form.querySelector('button[type="submit"]') : null;
            if (submitBtn) {
                submitBtn.textContent = 'تحديث الصفحة';
                console.log('✅ Submit button updated');
            }

            // Fill content editors (with delay for TinyMCE)
            setTimeout(() => {
                this.fillContentEditors(pageData);
            }, 1000);

            console.log('✅ Edit form filled successfully');

        } catch (error) {
            console.error('❌ Error filling edit form:', error);
        }
    },

    fillContentEditors(pageData) {
        console.log('📝 Filling content editors...');

        try {
            // Fill TinyMCE editors if available
            if (typeof tinymce !== 'undefined') {
                const rightEditor = tinymce.get('rightContent');
                const leftEditor = tinymce.get('leftContent');

                if (rightEditor && pageData.contenu_droit) {
                    rightEditor.setContent(pageData.contenu_droit);
                    console.log('✅ Right content editor filled');
                }

                if (leftEditor && pageData.contenu_gauche) {
                    leftEditor.setContent(pageData.contenu_gauche);
                    console.log('✅ Left content editor filled');
                }
            } else {
                console.warn('⚠️ TinyMCE not available');
            }

            // Show existing images
            if (pageData.images && pageData.images.length > 0) {
                this.showExistingImages(pageData.images);
            }

        } catch (error) {
            console.error('❌ Error filling content editors:', error);
        }
    },

    showExistingImages(images) {
        console.log('🖼️ Showing existing images:', images);

        if (!this.imagePreview) {
            console.warn('⚠️ Image preview container not found');
            return;
        }

        // Clear existing previews
        this.imagePreview.innerHTML = '';

        // Add each image
        images.forEach((imageUrl) => {
            const preview = document.createElement('div');
            preview.className = 'preview-image existing-image';
            preview.style.cssText = `
                width: 100px;
                height: 100px;
                background-image: url(${imageUrl});
                background-size: cover;
                background-position: center;
                border-radius: 5px;
                margin: 5px;
                display: inline-block;
                position: relative;
            `;

            const removeBtn = document.createElement('button');
            removeBtn.innerHTML = '❌';
            removeBtn.style.cssText = `
                position: absolute;
                top: -5px;
                right: -5px;
                background: red;
                color: white;
                border: none;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                cursor: pointer;
                font-size: 12px;
            `;

            removeBtn.onclick = () => {
                preview.remove();
                console.log('🗑️ Image marked for deletion:', imageUrl);
            };

            preview.appendChild(removeBtn);
            this.imagePreview.appendChild(preview);
        });

        console.log('✅ Existing images displayed');
    },

    populateEditForm(pageData) {
        console.log('📝 Populating edit form with:', pageData);

        // Set form to edit mode
        if (this.form) {
            this.form.setAttribute('data-edit-id', pageData.id);
        }

        // Update modal title
        const modalTitle = document.querySelector('#landingPageModal .modal-header h3');
        if (modalTitle) {
            modalTitle.textContent = 'تعديل صفحة الهبوط';
        }

        // Populate form fields
        const titleInput = document.getElementById('landingPageTitle');
        if (titleInput) {
            titleInput.value = pageData.titre || '';
        }

        const productSelect = document.getElementById('productSelect');
        if (productSelect) {
            productSelect.value = pageData.produit_id || '';
            // Trigger change event to update UI
            productSelect.dispatchEvent(new Event('change'));
        }

        // Populate TinyMCE editors
        setTimeout(() => {
            const rightEditor = tinymce.get('rightContent');
            const leftEditor = tinymce.get('leftContent');

            if (rightEditor) {
                rightEditor.setContent(pageData.contenu_droit || '');
            }
            if (leftEditor) {
                leftEditor.setContent(pageData.contenu_gauche || '');
            }
        }, 500);

        // Show existing images if any
        if (pageData.images && pageData.images.length > 0) {
            this.displayExistingImages(pageData.images);
        }

        // Update submit button text
        const submitBtn = this.form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.textContent = 'تحديث';
        }
    },

    displayExistingImages(images) {
        console.log('🖼️ Displaying existing images:', images);

        if (!this.imagePreview) return;

        this.imagePreview.innerHTML = '';

        images.forEach((imageUrl, index) => {
            const preview = document.createElement('div');
            preview.className = 'preview-image existing-image';
            preview.style.backgroundImage = `url(${imageUrl.toString()})`;
            preview.setAttribute('data-image-url', imageUrl);

            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-image';
            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
            removeBtn.onclick = () => {
                preview.remove();
                // Mark image for deletion
                const deletedImages = this.form.querySelector('input[name="deleted_images"]');
                if (!deletedImages) {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'deleted_images';
                    hiddenInput.value = imageUrl;
                    this.form.appendChild(hiddenInput);
                } else {
                    deletedImages.value = deletedImages.value + ',' + imageUrl.toString();
                }
            };

            preview.appendChild(removeBtn);
            this.imagePreview.appendChild(preview);
        });
    },

    async deletePage(id) {
        if (window.confirm('هل أنت متأكد من حذف صفحة الهبوط هذه؟')) {
            try {
                const response = await fetch(`../php/api/landing-pages.php?id=${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    notificationManager.showSuccess('تم حذف صفحة الهبوط بنجاح');
                    this.loadLandingPages();
                } else {
                    notificationManager.showError(result.message || 'حدث خطأ أثناء حذف صفحة الهبوط');
                }
            } catch (error) {
                console.error('Error deleting landing page:', error);
                notificationManager.showError('حدث خطأ أثناء حذف صفحة الهبوط');
            }
        }
    },

    async clonePage(pageId) {
        if (!window.confirm('هل تريد إنشاء نسخة من هذه الصفحة؟')) {
            return;
        }

        try {
            const response = await fetch(`../php/api/landing-pages.php?action=clone&id=${pageId}`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                notificationManager.showSuccess('تم إنشاء نسخة من الصفحة بنجاح');
                this.loadLandingPages();
            } else {
                notificationManager.showError(result.message || 'حدث خطأ أثناء نسخ الصفحة');
            }
        } catch (error) {
            console.error('Error cloning landing page:', error);
            notificationManager.showError('حدث خطأ أثناء نسخ الصفحة');
        }
    },

    // Debug function to help troubleshoot refresh issues
    debugRefresh() {
        console.log('🔍 Debug: Landing Pages Manager State');
        console.log('- Initialized:', this.initialized);
        console.log('- Modal element:', !!this.modal);
        console.log('- Form element:', !!this.form);
        console.log('- Container element:', !!document.getElementById('landingPagesContainer'));
        console.log('- Current section active:', document.querySelector('#landingPages.active') ? 'Yes' : 'No');

        // Force refresh
        console.log('🔄 Force refreshing landing pages...');
        this.loadLandingPages();
    }
};

// Social sharing functions
function sharePage(platform, url) {
    const title = encodeURIComponent(document.title);
    const encodedUrl = encodeURIComponent(url);

    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}`,
        whatsapp: `https://api.whatsapp.com/send?text=${title}%20${encodedUrl}`,
        telegram: `https://t.me/share/url?url=${encodedUrl}`
    };

    if (shareUrls.hasOwnProperty(platform)) {
        window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
}

// Copy URL to clipboard with visual feedback
function copyPageUrl(url, buttonElement = null) {
    // Ensure we have a complete URL
    let fullUrl = url;

    // If URL doesn't start with http, make it a complete URL
    if (url && !url.startsWith('http')) {
        // Get current domain and port
        const currentOrigin = window.location.origin; // e.g., http://localhost:8000

        // Remove leading slash if present to avoid double slashes
        const cleanUrl = url.startsWith('/') ? url.substring(1) : url;

        fullUrl = `${currentOrigin}/${cleanUrl}`;
    }

    console.log('📋 Copying URL:', fullUrl);

    if (!navigator || !navigator.clipboard) {
            notificationManager.showError('نسخ الرابط غير متوفر في هذا المتصفح');
            return;
        }

        navigator.clipboard.writeText(fullUrl).then(() => {
        notificationManager.showSuccess('تم نسخ الرابط بنجاح');
        if (buttonElement) {
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
            }, 2000);
        }
    }).catch(() => {
        notificationManager.showError('فشل في نسخ الرابط');
    });
}

// Function to add URL image input
function addUrlImageInput() {
    const container = document.getElementById('urlImagesContainer');
        if (!container) return;
    const newInput = document.createElement('div');
    newInput.className = 'url-image-input';
    newInput.style.cssText = 'display: flex; align-items: center; margin-bottom: 10px;';
    newInput.innerHTML = `
        <input
            type="url"
            name="imageUrls[]"
            placeholder="https://images.unsplash.com/photo-example.jpg"
            style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-left: 10px;"
        />
        <button
            type="button"
            onclick="removeUrlImageInput(this)"
            class="remove-btn"
            style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; margin-left: 5px;"
            title="حذف هذه الصورة"
        >
            ❌
        </button>
        <button
            type="button"
            onclick="addUrlImageInput()"
            class="add-btn"
            style="background: #28a745; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;"
            title="إضافة صورة أخرى"
        >
            ➕
        </button>
    `;
    container.appendChild(newInput);
}

function removeUrlImageInput(button) {
    if (!(button instanceof HTMLElement)) return;
    const container = document.getElementById('urlImagesContainer');
    const inputs = container.querySelectorAll('.url-image-input');

    // Keep at least one input
    if (inputs.length > 1) {
        button.parentElement.remove();
    } else {
        // Clear the input instead of removing it
        const input = button.parentElement.querySelector('input');
        input.value = '';
    }
}

// Make landingPagesManager globally accessible
if (typeof window !== 'undefined') {
    window.landingPagesManager = landingPagesManager;
}

// Single initialization - prevent duplicates
if (typeof window !== 'undefined' && !window.landingPagesInitialized) {
    window.landingPagesInitialized = true;

    // Initialize when DOM is loaded or immediately if already loaded
    if (typeof document !== 'undefined' && document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            landingPagesManager.init();
        });
    } else {
        landingPagesManager.init();
    }
}

// Global debug function for troubleshooting
if (typeof window !== 'undefined') {
    window.debugLandingPages = function() {
        if ('landingPagesManager' in window) {
            landingPagesManager.debugRefresh();
        } else {
            console.error('Landing Pages Manager not available');
        }
    };
}

// Global force refresh function
if (typeof window !== 'undefined') {
    window.forceRefreshLandingPages = function() {
        if (typeof window !== 'undefined' && window.landingPagesManager) {
            console.log('🔄 Force refreshing landing pages from global function...');
            landingPagesManager.loadLandingPages();
        } else {
            console.error('Landing Pages Manager not available');
        }
    };
}
